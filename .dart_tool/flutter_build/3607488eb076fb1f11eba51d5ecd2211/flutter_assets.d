 /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/pubspec.yaml /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf /Users/<USER>/Downloads/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /Users/<USER>/Downloads/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/.dart_tool/flutter_build/3607488eb076fb1f11eba51d5ecd2211/native_assets.json /Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.55/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_custom_tabs-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_custom_tabs_android-2.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_custom_tabs_ios-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_custom_tabs_platform_interface-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_custom_tabs_web-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-5.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-8.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE /Users/<USER>/Downloads/flutter/bin/cache/pkg/sky_engine/LICENSE /Users/<USER>/Downloads/flutter/packages/flutter/LICENSE /Users/<USER>/Documents/augment-projects/Nepali\ Result\ All-in-One/DOES_NOT_EXIST_RERUN_FOR_WILDCARD520652120
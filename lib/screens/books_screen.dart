import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class BooksScreen extends StatefulWidget {
  const BooksScreen({super.key});

  @override
  State<BooksScreen> createState() => _BooksScreenState();
}

class _BooksScreenState extends State<BooksScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Textbooks'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Class 10'),
            Tab(text: 'Class 11'),
            Tab(text: 'Class 12'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildClassBooks(10),
          _buildClassBooks(11),
          _buildClassBooks(12),
        ],
      ),
    );
  }

  Widget _buildClassBooks(int classNumber) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Class $classNumber Textbooks',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.orange[700]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Some books may take longer to load due to internet speed or source. We\'re working to fix it.',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          _buildMediumSection('English Medium', classNumber, 'english'),
          const SizedBox(height: 24),
          _buildMediumSection('Nepali Medium', classNumber, 'nepali'),
        ],
      ),
    );
  }

  Widget _buildMediumSection(String medium, int classNumber, String mediumType) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: mediumType == 'english' 
                ? [Colors.blue[600]!, Colors.blue[400]!]
                : [Colors.red[600]!, Colors.red[400]!],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Image.asset(
                mediumType == 'english' 
                  ? 'assets/flags/uk_flag.svg'
                  : 'assets/flags/nepal_flag.svg',
                width: 32,
                height: 24,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    mediumType == 'english' ? Icons.language : Icons.flag,
                    color: Colors.white,
                    size: 24,
                  );
                },
              ),
              const SizedBox(width: 12),
              Text(
                medium,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              const Icon(
                Icons.arrow_forward_ios,
                color: Colors.white,
                size: 16,
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          children: _getSubjectsForClass(classNumber, mediumType).map((subject) {
            return _buildBookCard(subject);
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildBookCard(Map<String, String> subject) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _openBookUrl(subject['url']!),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.book,
                size: 48,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 8),
              Text(
                subject['name']!,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                'PDF Book',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Map<String, String>> _getSubjectsForClass(int classNumber, String medium) {
    // This would normally come from a database or API
    // For now, using sample data based on common Nepali curriculum
    
    if (classNumber == 10) {
      return [
        {'name': 'Mathematics', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Science', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'English', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Nepali', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Social Studies', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Computer Science', 'url': 'https://moecdc.gov.np/en/text-books'},
      ];
    } else if (classNumber == 11) {
      return [
        {'name': 'Physics', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Chemistry', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Biology', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Mathematics', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'English', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Nepali', 'url': 'https://moecdc.gov.np/en/text-books'},
      ];
    } else {
      return [
        {'name': 'Physics', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Chemistry', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Biology', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Mathematics', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'English', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Nepali', 'url': 'https://moecdc.gov.np/en/text-books'},
      ];
    }
  }

  Future<void> _openBookUrl(String url) async {
    try {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open book: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

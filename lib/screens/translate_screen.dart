import 'package:flutter/material.dart';

class TranslateScreen extends StatefulWidget {
  const TranslateScreen({super.key});

  @override
  State<TranslateScreen> createState() => _TranslateScreenState();
}

class _TranslateScreenState extends State<TranslateScreen> {
  final TextEditingController _inputController = TextEditingController();
  final TextEditingController _outputController = TextEditingController();
  bool isNepaliToEnglish = true;

  // Simple word translations (in a real app, you'd use a translation API)
  final Map<String, String> nepaliToEnglish = {
    'नमस्ते': 'Hello',
    'धन्यवाद': 'Thank you',
    'माफ गर्नुहोस्': 'Sorry',
    'पानी': 'Water',
    'खाना': 'Food',
    'घर': 'House',
    'स्कूल': 'School',
    'किताब': 'Book',
    'साथी': 'Friend',
    'परिवार': 'Family',
  };

  final Map<String, String> englishToNepali = {
    'hello': 'नमस्ते',
    'thank you': 'धन्यवाद',
    'sorry': 'माफ गर्नुहोस्',
    'water': 'पानी',
    'food': 'खाना',
    'house': 'घर',
    'school': 'स्कूल',
    'book': 'किताब',
    'friend': 'साथी',
    'family': 'परिवार',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Translate'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        isNepaliToEnglish ? 'Nepali' : 'English',
                        style: Theme.of(context).textTheme.titleMedium,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.swap_horiz),
                      onPressed: () {
                        setState(() {
                          isNepaliToEnglish = !isNepaliToEnglish;
                          _inputController.clear();
                          _outputController.clear();
                        });
                      },
                    ),
                    Expanded(
                      child: Text(
                        isNepaliToEnglish ? 'English' : 'Nepali',
                        style: Theme.of(context).textTheme.titleMedium,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Enter text to translate:',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _inputController,
                        maxLines: 5,
                        decoration: InputDecoration(
                          hintText: isNepaliToEnglish 
                            ? 'नेपाली पाठ लेख्नुहोस्...'
                            : 'Enter English text...',
                          border: const OutlineInputBorder(),
                        ),
                        onChanged: _translateText,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Translation:',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _outputController,
                        maxLines: 5,
                        readOnly: true,
                        decoration: InputDecoration(
                          hintText: 'Translation will appear here...',
                          border: const OutlineInputBorder(),
                          fillColor: Colors.grey.shade50,
                          filled: true,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Note:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This is a basic word-by-word translator. For better translations, consider using Google Translate or other professional translation services.',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _translateText(String text) {
    if (text.isEmpty) {
      _outputController.clear();
      return;
    }

    String translation = '';
    List<String> words = text.toLowerCase().split(' ');
    
    for (String word in words) {
      String translatedWord = '';
      
      if (isNepaliToEnglish) {
        translatedWord = nepaliToEnglish[word] ?? word;
      } else {
        translatedWord = englishToNepali[word] ?? word;
      }
      
      translation += '$translatedWord ';
    }
    
    _outputController.text = translation.trim();
  }

  @override
  void dispose() {
    _inputController.dispose();
    _outputController.dispose();
    super.dispose();
  }
}

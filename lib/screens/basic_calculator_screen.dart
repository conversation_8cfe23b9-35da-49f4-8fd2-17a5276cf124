import 'package:flutter/material.dart';

class BasicCalculatorScreen extends StatefulWidget {
  const BasicCalculatorScreen({super.key});

  @override
  State<BasicCalculatorScreen> createState() => _BasicCalculatorScreenState();
}

class _BasicCalculatorScreenState extends State<BasicCalculatorScreen> {
  String display = '0';
  String previousNumber = '';
  String operation = '';
  bool waitingForOperand = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calculator'),
      ),
      body: Column(
        children: [
          // Display
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            color: Theme.of(context).colorScheme.surface,
            child: Text(
              display,
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                fontSize: 48,
                fontWeight: FontWeight.w300,
              ),
              textAlign: TextAlign.right,
            ),
          ),
          // Buttons
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Column(
                children: [
                  _buildButtonRow(['C', '±', '%', '÷']),
                  _buildButtonRow(['7', '8', '9', '×']),
                  _buildButtonRow(['4', '5', '6', '-']),
                  _buildButtonRow(['1', '2', '3', '+']),
                  _buildButtonRow(['0', '.', '=']),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonRow(List<String> buttons) {
    return Expanded(
      child: Row(
        children: buttons.map((button) {
          return Expanded(
            flex: button == '0' ? 2 : 1,
            child: Container(
              margin: const EdgeInsets.all(4),
              child: ElevatedButton(
                onPressed: () => _onButtonPressed(button),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _getButtonColor(button),
                  foregroundColor: _getButtonTextColor(button),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.all(20),
                ),
                child: Text(
                  button,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Color _getButtonColor(String button) {
    if (['÷', '×', '-', '+', '='].contains(button)) {
      return Theme.of(context).colorScheme.primary;
    } else if (['C', '±', '%'].contains(button)) {
      return Colors.grey.shade300;
    } else {
      return Colors.grey.shade100;
    }
  }

  Color _getButtonTextColor(String button) {
    if (['÷', '×', '-', '+', '='].contains(button)) {
      return Colors.white;
    } else {
      return Colors.black;
    }
  }

  void _onButtonPressed(String button) {
    setState(() {
      if (button == 'C') {
        _clear();
      } else if (button == '±') {
        _toggleSign();
      } else if (button == '%') {
        _percentage();
      } else if (['÷', '×', '-', '+'].contains(button)) {
        _setOperation(button);
      } else if (button == '=') {
        _calculate();
      } else if (button == '.') {
        _addDecimal();
      } else {
        _addNumber(button);
      }
    });
  }

  void _clear() {
    display = '0';
    previousNumber = '';
    operation = '';
    waitingForOperand = false;
  }

  void _toggleSign() {
    if (display != '0') {
      if (display.startsWith('-')) {
        display = display.substring(1);
      } else {
        display = '-$display';
      }
    }
  }

  void _percentage() {
    double value = double.tryParse(display) ?? 0;
    display = (value / 100).toString();
    if (display.endsWith('.0')) {
      display = display.substring(0, display.length - 2);
    }
  }

  void _setOperation(String op) {
    if (previousNumber.isNotEmpty && operation.isNotEmpty && !waitingForOperand) {
      _calculate();
    }
    
    previousNumber = display;
    operation = op;
    waitingForOperand = true;
  }

  void _calculate() {
    if (previousNumber.isEmpty || operation.isEmpty) return;
    
    double prev = double.tryParse(previousNumber) ?? 0;
    double current = double.tryParse(display) ?? 0;
    double result = 0;
    
    switch (operation) {
      case '+':
        result = prev + current;
        break;
      case '-':
        result = prev - current;
        break;
      case '×':
        result = prev * current;
        break;
      case '÷':
        if (current != 0) {
          result = prev / current;
        } else {
          display = 'Error';
          return;
        }
        break;
    }
    
    display = result.toString();
    if (display.endsWith('.0')) {
      display = display.substring(0, display.length - 2);
    }
    
    previousNumber = '';
    operation = '';
    waitingForOperand = true;
  }

  void _addDecimal() {
    if (waitingForOperand) {
      display = '0.';
      waitingForOperand = false;
    } else if (!display.contains('.')) {
      display += '.';
    }
  }

  void _addNumber(String number) {
    if (waitingForOperand) {
      display = number;
      waitingForOperand = false;
    } else {
      display = display == '0' ? number : display + number;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../widgets/result_card.dart';

class ResultsScreen extends StatelessWidget {
  const ResultsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nepali Results'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Navigate to notifications
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuSelection(context, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'about',
                child: ListTile(
                  leading: Icon(Icons.info),
                  title: Text('About'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('Share App'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'rate',
                child: ListTile(
                  leading: Icon(Icons.star),
                  title: Text('Rate App'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Check Your Results',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Select your exam type to check results',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.1,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                ResultCard(
                  title: 'SEE Results',
                  subtitle: 'Secondary Education',
                  imagePath: 'assets/images/see_results2.png',
                  onTap: () => _openUrl('https://see.gov.np'),
                ),
                ResultCard(
                  title: 'HSEB Results',
                  subtitle: 'Higher Secondary',
                  imagePath: 'assets/images/hseb_results.png',
                  onTap: () => _openUrl('https://neb.gov.np'),
                ),
                ResultCard(
                  title: 'TU Results',
                  subtitle: 'Tribhuvan University',
                  imagePath: 'assets/images/tu_results.png',
                  onTap: () => _openUrl('https://tribhuvan-university.edu.np'),
                ),
                ResultCard(
                  title: 'KU Results',
                  subtitle: 'Kathmandu University',
                  imagePath: 'assets/images/ku_results.png',
                  onTap: () => _openUrl('https://ku.edu.np'),
                ),
                ResultCard(
                  title: 'PU Results',
                  subtitle: 'Purbanchal University',
                  imagePath: 'assets/images/pu_results.png',
                  onTap: () => _openUrl('https://purbuniv.edu.np'),
                ),
                ResultCard(
                  title: 'CTEVT Results',
                  subtitle: 'Technical Education',
                  imagePath: 'assets/images/ctevt_results.png',
                  onTap: () => _openUrl('https://ctevt.org.np'),
                ),
                ResultCard(
                  title: 'DV Lottery',
                  subtitle: 'US Diversity Visa',
                  imagePath: 'assets/images/dv_lottery_result.png',
                  onTap: () => _openUrl('https://dvprogram.state.gov'),
                ),
                ResultCard(
                  title: 'IPO Results',
                  subtitle: 'Share Market',
                  imagePath: 'assets/images/official site.png',
                  onTap: () => _showIpoOptions(context),
                ),
              ],
            ),
            const SizedBox(height: 32),
            _buildQuickLinksSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickLinksSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Links',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildQuickLinkCard(
                context,
                'NTC Portal',
                'assets/images/nepal telecom.png',
                () => _openUrl('https://ntc.net.np'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildQuickLinkCard(
                context,
                'Kantipur',
                'assets/images/ekantipur.png',
                () => _openUrl('https://ekantipur.com'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickLinkCard(BuildContext context, String title, String imagePath, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Image.asset(
                imagePath,
                height: 40,
                width: 40,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.link,
                    size: 40,
                    color: Theme.of(context).colorScheme.primary,
                  );
                },
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showIpoOptions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('IPO Results'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.link),
              title: const Text('IPO Result CDSC'),
              subtitle: const Text('https://iporesult.cdsc.com.np'),
              onTap: () {
                Navigator.pop(context);
                _openUrl('https://iporesult.cdsc.com.np');
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.link),
              title: const Text('Meroshare'),
              subtitle: const Text('https://meroshare.cdsc.com.np'),
              onTap: () {
                Navigator.pop(context);
                _openUrl('https://meroshare.cdsc.com.np');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<void> _openUrl(String url) async {
    try {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      print('Could not launch $url');
    }
  }

  void _handleMenuSelection(BuildContext context, String value) {
    switch (value) {
      case 'about':
        _showAboutDialog(context);
        break;
      case 'share':
        // Share app functionality
        break;
      case 'rate':
        _openUrl('https://play.google.com/store');
        break;
    }
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About Nepali Results'),
        content: const Text(
          'Complete solution for Nepali educational results and student tools.\n\n'
          'Features:\n'
          '• SEE, HSEB, TU, KU, PU, CTEVT Results\n'
          '• DV Lottery & IPO Results\n'
          '• Educational Books\n'
          '• Student Tools & Calculators\n'
          '• And much more!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

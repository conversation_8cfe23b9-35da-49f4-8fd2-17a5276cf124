import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class NepaliDateConverterScreen extends StatefulWidget {
  const NepaliDateConverterScreen({super.key});

  @override
  State<NepaliDateConverterScreen> createState() => _NepaliDateConverterScreenState();
}

class _NepaliDateConverterScreenState extends State<NepaliDateConverterScreen> {
  DateTime _selectedDate = DateTime.now();
  String _nepaliDate = '';
  bool _isAdToNepali = true;

  // Nepali months
  final List<String> _nepaliMonths = [
    'बैशाख', 'जेठ', 'असार', 'साउन', 'भदौ', 'असोज',
    'कार्तिक', 'मंसिर', 'पुष', 'माघ', 'फाल्गुन', 'चैत'
  ];

  // Nepali weekdays
  final List<String> _nepaliWeekdays = [
    'आइतबार', 'सोमबार', 'मंगलबार', 'बुधबार', 'बिहिबार', 'शुक्रबार', 'शनिबार'
  ];

  // Nepali numerals
  final List<String> _nepaliNumerals = [
    '०', '१', '२', '३', '४', '५', '६', '७', '८', '९'
  ];

  @override
  void initState() {
    super.initState();
    _convertDate();
  }

  String _toNepaliNumber(int number) {
    String englishNumber = number.toString();
    String nepaliNumber = '';
    
    for (int i = 0; i < englishNumber.length; i++) {
      int digit = int.parse(englishNumber[i]);
      nepaliNumber += _nepaliNumerals[digit];
    }
    
    return nepaliNumber;
  }

  void _convertDate() {
    if (_isAdToNepali) {
      _convertAdToNepali();
    } else {
      _convertNepaliToAd();
    }
  }

  void _convertAdToNepali() {
    // Simplified BS conversion (approximate)
    // This is a basic conversion - for production, use a proper BS calendar library
    int bsYear = _selectedDate.year + 57; // Approximate conversion
    int bsMonth = _selectedDate.month;
    int bsDay = _selectedDate.day;
    
    // Adjust for Nepali calendar differences
    if (_selectedDate.month >= 4) {
      bsMonth = _selectedDate.month - 3;
    } else {
      bsMonth = _selectedDate.month + 9;
      bsYear--;
    }
    
    // Ensure month is within valid range
    if (bsMonth > 12) {
      bsMonth = bsMonth - 12;
      bsYear++;
    }
    if (bsMonth < 1) {
      bsMonth = bsMonth + 12;
      bsYear--;
    }

    // Get weekday
    String weekday = _nepaliWeekdays[_selectedDate.weekday % 7];
    
    // Format the date
    String nepaliYear = _toNepaliNumber(bsYear);
    String nepaliMonthName = _nepaliMonths[bsMonth - 1];
    String nepaliDayNumber = _toNepaliNumber(bsDay);
    
    setState(() {
      _nepaliDate = '$nepaliYear $nepaliMonthName $nepaliDayNumber, $weekday';
    });
  }

  void _convertNepaliToAd() {
    // This is a simplified reverse conversion
    // In a real app, you'd use a proper calendar conversion library
    setState(() {
      _nepaliDate = 'Nepali to AD conversion coming soon!';
    });
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _convertDate();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nepali Date Converter'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Conversion Type Toggle
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Conversion Type',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<bool>(
                            title: const Text('AD to Nepali'),
                            value: true,
                            groupValue: _isAdToNepali,
                            onChanged: (value) {
                              setState(() {
                                _isAdToNepali = value!;
                              });
                              _convertDate();
                            },
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<bool>(
                            title: const Text('Nepali to AD'),
                            value: false,
                            groupValue: _isAdToNepali,
                            onChanged: (value) {
                              setState(() {
                                _isAdToNepali = value!;
                              });
                              _convertDate();
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Date Input
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isAdToNepali ? 'English Date (AD)' : 'Nepali Date (BS)',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    InkWell(
                      onTap: _isAdToNepali ? _selectDate : null,
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              _isAdToNepali 
                                ? DateFormat('MMMM d, yyyy (EEEE)').format(_selectedDate)
                                : 'Select Nepali Date',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Conversion Result
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isAdToNepali ? 'Nepali Date (BS)' : 'English Date (AD)',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                        ),
                      ),
                      child: Text(
                        _nepaliDate.isNotEmpty ? _nepaliDate : 'Select a date to convert',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Information Card
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue[700]),
                        const SizedBox(width: 8),
                        Text(
                          'Information',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• This converter uses an approximate conversion algorithm\n'
                      '• For official purposes, please verify with authoritative sources\n'
                      '• Nepali calendar follows lunar months with varying days\n'
                      '• The Nepali New Year typically falls in mid-April',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

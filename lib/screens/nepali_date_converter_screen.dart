import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class NepaliDateConverterScreen extends StatefulWidget {
  const NepaliDateConverterScreen({super.key});

  @override
  State<NepaliDateConverterScreen> createState() => _NepaliDateConverterScreenState();
}

class _NepaliDateConverterScreenState extends State<NepaliDateConverterScreen> {
  DateTime selectedDate = DateTime.now();
  String nepaliDate = '';
  bool isEnglishToNepali = true;

  @override
  void initState() {
    super.initState();
    _convertDate();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nepali Date Converter'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<bool>(
                            title: const Text('English to Nepali'),
                            value: true,
                            groupValue: isEnglishToNepali,
                            onChanged: (value) {
                              setState(() {
                                isEnglishToNepali = value!;
                                _convertDate();
                              });
                            },
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<bool>(
                            title: const Text('Nepali to English'),
                            value: false,
                            groupValue: isEnglishToNepali,
                            onChanged: (value) {
                              setState(() {
                                isEnglishToNepali = value!;
                                _convertDate();
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      title: Text(isEnglishToNepali ? 'English Date' : 'Nepali Date'),
                      subtitle: Text(
                        isEnglishToNepali 
                          ? DateFormat('yyyy-MM-dd').format(selectedDate)
                          : nepaliDate.isNotEmpty ? nepaliDate : 'Select date',
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: _selectDate,
                    ),
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isEnglishToNepali ? 'Nepali Date:' : 'English Date:',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            isEnglishToNepali 
                              ? nepaliDate.isNotEmpty ? nepaliDate : 'Converting...'
                              : DateFormat('yyyy-MM-dd').format(selectedDate),
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About Nepali Calendar',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'The Nepali calendar (Bikram Sambat) is approximately 56 years and 8-9 months ahead of the Gregorian calendar. '
                      'It is the official calendar of Nepal and is widely used for cultural and religious events.',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );
    
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
        _convertDate();
      });
    }
  }

  void _convertDate() {
    // This is a simplified conversion - in a real app, you'd use a proper Nepali calendar library
    if (isEnglishToNepali) {
      // Convert English to Nepali
      final year = selectedDate.year + 56;
      final month = selectedDate.month;
      final day = selectedDate.day;
      
      final nepaliMonths = [
        'बैशाख', 'जेठ', 'आषाढ', 'श्रावण', 'भाद्र', 'आश्विन',
        'कार्तिक', 'मंसिर', 'पौष', 'माघ', 'फाल्गुन', 'चैत्र'
      ];
      
      setState(() {
        nepaliDate = '$year ${nepaliMonths[(month - 1) % 12]} $day';
      });
    } else {
      // Convert Nepali to English (simplified)
      setState(() {
        nepaliDate = 'Conversion feature coming soon';
      });
    }
  }
}

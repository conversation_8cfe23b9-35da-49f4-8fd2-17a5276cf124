import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotepadScreen extends StatefulWidget {
  const NotepadScreen({super.key});

  @override
  State<NotepadScreen> createState() => _NotepadScreenState();
}

class _NotepadScreenState extends State<NotepadScreen> {
  final TextEditingController _textController = TextEditingController();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNote();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notepad'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveNote,
          ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearNote,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          const Icon(Icons.info, color: Colors.blue),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Your notes are automatically saved locally on your device.',
                              style: TextStyle(color: Colors.blue.shade700),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: TextField(
                      controller: _textController,
                      maxLines: null,
                      expands: true,
                      decoration: const InputDecoration(
                        hintText: 'Start typing your notes here...',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.all(16),
                      ),
                      style: const TextStyle(
                        fontSize: 16,
                        height: 1.5,
                      ),
                      onChanged: (text) {
                        // Auto-save after a delay
                        _autoSave();
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _saveNote,
                          icon: const Icon(Icons.save),
                          label: const Text('Save Note'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _clearNote,
                          icon: const Icon(Icons.clear),
                          label: const Text('Clear All'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Future<void> _loadNote() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedNote = prefs.getString('notepad_content') ?? '';
      
      setState(() {
        _textController.text = savedNote;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error loading note'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveNote() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('notepad_content', _textController.text);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Note saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error saving note'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _clearNote() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Note'),
        content: const Text('Are you sure you want to clear all notes? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _textController.clear();
              });
              _saveNote();
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _autoSave() {
    // Simple auto-save implementation
    Future.delayed(const Duration(seconds: 2), () {
      _saveNote();
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}

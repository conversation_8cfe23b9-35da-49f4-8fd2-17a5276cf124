import 'package:flutter/material.dart';
import 'dart:math' as math;

class CalculatorScreen extends StatefulWidget {
  const CalculatorScreen({super.key});

  @override
  State<CalculatorScreen> createState() => _CalculatorScreenState();
}

class _CalculatorScreenState extends State<CalculatorScreen> {
  String _display = '0';
  String _previousNumber = '';
  String _operation = '';
  bool _waitingForOperand = false;

  void _inputNumber(String number) {
    setState(() {
      if (_waitingForOperand) {
        _display = number;
        _waitingForOperand = false;
      } else {
        _display = _display == '0' ? number : _display + number;
      }
    });
  }

  void _inputOperation(String nextOperation) {
    double inputValue = double.parse(_display);

    if (_previousNumber.isEmpty) {
      _previousNumber = inputValue.toString();
    } else if (_operation.isNotEmpty) {
      double previousValue = double.parse(_previousNumber);
      double result = _calculate(previousValue, inputValue, _operation);
      
      setState(() {
        _display = result.toString();
        _previousNumber = result.toString();
      });
    }

    setState(() {
      _waitingForOperand = true;
      _operation = nextOperation;
    });
  }

  double _calculate(double firstOperand, double secondOperand, String operation) {
    switch (operation) {
      case '+':
        return firstOperand + secondOperand;
      case '-':
        return firstOperand - secondOperand;
      case '×':
        return firstOperand * secondOperand;
      case '÷':
        return firstOperand / secondOperand;
      default:
        return secondOperand;
    }
  }

  void _performCalculation() {
    double inputValue = double.parse(_display);

    if (_previousNumber.isNotEmpty && _operation.isNotEmpty) {
      double previousValue = double.parse(_previousNumber);
      double result = _calculate(previousValue, inputValue, _operation);
      
      setState(() {
        _display = result.toString();
        _previousNumber = '';
        _operation = '';
        _waitingForOperand = true;
      });
    }
  }

  void _clear() {
    setState(() {
      _display = '0';
      _previousNumber = '';
      _operation = '';
      _waitingForOperand = false;
    });
  }

  void _clearEntry() {
    setState(() {
      _display = '0';
    });
  }

  void _inputDecimal() {
    if (_waitingForOperand) {
      setState(() {
        _display = '0.';
        _waitingForOperand = false;
      });
    } else if (!_display.contains('.')) {
      setState(() {
        _display = _display + '.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calculator'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Display
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              color: Colors.black,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (_previousNumber.isNotEmpty && _operation.isNotEmpty)
                    Text(
                      '$_previousNumber $_operation',
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 20,
                      ),
                    ),
                  Text(
                    _display,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 48,
                      fontWeight: FontWeight.w300,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
          // Buttons
          Expanded(
            flex: 3,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Column(
                children: [
                  _buildButtonRow(['C', 'CE', '⌫', '÷']),
                  _buildButtonRow(['7', '8', '9', '×']),
                  _buildButtonRow(['4', '5', '6', '-']),
                  _buildButtonRow(['1', '2', '3', '+']),
                  _buildButtonRow(['0', '.', '=']),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonRow(List<String> buttons) {
    return Expanded(
      child: Row(
        children: buttons.map((button) => _buildButton(button)).toList(),
      ),
    );
  }

  Widget _buildButton(String text) {
    Color? buttonColor;
    Color? textColor = Colors.black;
    
    if (text == 'C' || text == 'CE' || text == '⌫') {
      buttonColor = Colors.grey[300];
    } else if (['+', '-', '×', '÷', '='].contains(text)) {
      buttonColor = Theme.of(context).colorScheme.primary;
      textColor = Colors.white;
    } else {
      buttonColor = Colors.grey[100];
    }

    return Expanded(
      flex: text == '0' ? 2 : 1,
      child: Container(
        margin: const EdgeInsets.all(4),
        child: ElevatedButton(
          onPressed: () => _onButtonPressed(text),
          style: ElevatedButton.styleFrom(
            backgroundColor: buttonColor,
            foregroundColor: textColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.all(20),
          ),
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }

  void _onButtonPressed(String text) {
    switch (text) {
      case 'C':
        _clear();
        break;
      case 'CE':
        _clearEntry();
        break;
      case '⌫':
        if (_display.length > 1) {
          setState(() {
            _display = _display.substring(0, _display.length - 1);
          });
        } else {
          setState(() {
            _display = '0';
          });
        }
        break;
      case '.':
        _inputDecimal();
        break;
      case '=':
        _performCalculation();
        break;
      case '+':
      case '-':
      case '×':
      case '÷':
        _inputOperation(text);
        break;
      default:
        _inputNumber(text);
        break;
    }
  }
}

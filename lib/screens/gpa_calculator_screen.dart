import 'package:flutter/material.dart';

class GpaCalculatorScreen extends StatefulWidget {
  final bool isSEE;
  
  const GpaCalculatorScreen({super.key, this.isSEE = false});

  @override
  State<GpaCalculatorScreen> createState() => _GpaCalculatorScreenState();
}

class _GpaCalculatorScreenState extends State<GpaCalculatorScreen> {
  List<Map<String, dynamic>> _subjects = [];
  double _gpa = 0.0;
  String _grade = '';

  @override
  void initState() {
    super.initState();
    _initializeSubjects();
  }

  void _initializeSubjects() {
    if (widget.isSEE) {
      _subjects = [
        {'name': 'Nepali', 'marks': 0, 'fullMarks': 100},
        {'name': 'English', 'marks': 0, 'fullMarks': 100},
        {'name': 'Mathematics', 'marks': 0, 'fullMarks': 100},
        {'name': 'Science', 'marks': 0, 'fullMarks': 100},
        {'name': 'Social Studies', 'marks': 0, 'fullMarks': 100},
        {'name': 'Health & Physical Education', 'marks': 0, 'fullMarks': 100},
        {'name': 'Optional Subject', 'marks': 0, 'fullMarks': 100},
      ];
    } else {
      _subjects = [
        {'name': 'English', 'marks': 0, 'fullMarks': 100},
        {'name': 'Nepali', 'marks': 0, 'fullMarks': 100},
        {'name': 'Physics', 'marks': 0, 'fullMarks': 100},
        {'name': 'Chemistry', 'marks': 0, 'fullMarks': 100},
        {'name': 'Biology/Computer', 'marks': 0, 'fullMarks': 100},
        {'name': 'Mathematics', 'marks': 0, 'fullMarks': 100},
      ];
    }
  }

  void _calculateGPA() {
    double totalGradePoints = 0;
    int totalSubjects = 0;

    for (var subject in _subjects) {
      int marks = subject['marks'];
      if (marks > 0) {
        double gradePoint = _getGradePoint(marks, widget.isSEE);
        totalGradePoints += gradePoint;
        totalSubjects++;
      }
    }

    if (totalSubjects > 0) {
      setState(() {
        _gpa = totalGradePoints / totalSubjects;
        _grade = _getGrade(_gpa, widget.isSEE);
      });
    }
  }

  double _getGradePoint(int marks, bool isSEE) {
    if (isSEE) {
      // SEE grading system
      if (marks >= 90) return 4.0;
      if (marks >= 80) return 3.6;
      if (marks >= 70) return 3.2;
      if (marks >= 60) return 2.8;
      if (marks >= 50) return 2.4;
      if (marks >= 40) return 2.0;
      if (marks >= 32) return 1.6;
      return 0.0;
    } else {
      // NEB grading system
      if (marks >= 80) return 4.0;
      if (marks >= 70) return 3.6;
      if (marks >= 60) return 3.2;
      if (marks >= 50) return 2.8;
      if (marks >= 40) return 2.4;
      if (marks >= 32) return 2.0;
      return 0.0;
    }
  }

  String _getGrade(double gpa, bool isSEE) {
    if (gpa >= 3.6) return 'A+';
    if (gpa >= 3.2) return 'A';
    if (gpa >= 2.8) return 'B+';
    if (gpa >= 2.4) return 'B';
    if (gpa >= 2.0) return 'C+';
    if (gpa >= 1.6) return 'C';
    if (gpa >= 1.2) return 'D+';
    if (gpa >= 0.8) return 'D';
    return 'F';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.isSEE ? 'SEE' : 'NEB'} GPA Calculator'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _initializeSubjects();
                _gpa = 0.0;
                _grade = '';
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Result Display
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.secondary,
                ],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                const Text(
                  'Your GPA',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _gpa.toStringAsFixed(2),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (_grade.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Grade: $_grade',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          // Subjects List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _subjects.length,
              itemBuilder: (context, index) {
                final subject = _subjects[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            subject['name'],
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: 'Marks',
                              border: const OutlineInputBorder(),
                              suffixText: '/${subject['fullMarks']}',
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                            onChanged: (value) {
                              setState(() {
                                subject['marks'] = int.tryParse(value) ?? 0;
                              });
                              _calculateGPA();
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        Container(
                          width: 60,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getGradeColor(subject['marks']),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _getSubjectGrade(subject['marks']),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Enter your marks for each subject to calculate your GPA.\n'
              'Grading System: ${widget.isSEE ? 'SEE' : 'NEB'} Standard',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  String _getSubjectGrade(int marks) {
    if (marks == 0) return '-';
    double gp = _getGradePoint(marks, widget.isSEE);
    return _getGrade(gp, widget.isSEE);
  }

  Color _getGradeColor(int marks) {
    if (marks == 0) return Colors.grey;
    if (marks >= 80) return Colors.green;
    if (marks >= 60) return Colors.blue;
    if (marks >= 40) return Colors.orange;
    return Colors.red;
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_custom_tabs/flutter_custom_tabs.dart' as custom_tabs;
import 'package:url_launcher/url_launcher.dart';

class SeeResultsScreen extends StatelessWidget {
  const SeeResultsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SEE Results'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.secondary,
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.school,
                    size: 48,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'SEE Results',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Text(
                    'Secondary Education Examination',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Official Websites Section
            _buildSection(
              context,
              'Official Websites',
              Icons.language,
              [
                _buildWebsiteCard(
                  context,
                  'SEE Official Portal',
                  'https://see.gov.np',
                  Icons.school,
                  'Primary official website for SEE results',
                ),
                _buildWebsiteCard(
                  context,
                  'NTC Portal',
                  'https://ntc.net.np',
                  Icons.network_cell,
                  'Nepal Telecom result portal',
                ),
                _buildWebsiteCard(
                  context,
                  'Kantipur Portal',
                  'https://ekantipur.com',
                  Icons.newspaper,
                  'Kantipur news result portal',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // SMS Method Section
            _buildSection(
              context,
              'SMS Method',
              Icons.sms,
              [
                _buildSmsCard(
                  context,
                  'NTC Users',
                  'SEE <symbol no>',
                  '1600',
                  'Send SMS to 1600',
                ),
                _buildSmsCard(
                  context,
                  'Ncell Users',
                  'SEE <symbol no>',
                  '1600',
                  'Send SMS to 1600',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // IVR Method Section
            _buildSection(
              context,
              'IVR Method',
              Icons.phone,
              [
                _buildIvrCard(
                  context,
                  'Voice Call',
                  '1601',
                  'Call 1601 for voice results',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildWebsiteCard(BuildContext context, String title, String url, IconData icon, String description) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Text(description),
        trailing: const Icon(Icons.open_in_new),
        onTap: () => _launchInCustomTabs(context, url),
      ),
    );
  }

  Widget _buildSmsCard(BuildContext context, String provider, String format, String number, String description) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(Icons.sms, color: Theme.of(context).colorScheme.primary),
        title: Text(provider, style: const TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Format: $format'),
            Text('Send to: $number'),
            Text(description, style: TextStyle(color: Colors.grey[600])),
          ],
        ),
        trailing: ElevatedButton.icon(
          onPressed: () => _sendSms(context, format, number),
          icon: const Icon(Icons.send, size: 16),
          label: const Text('Send SMS'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildIvrCard(BuildContext context, String title, String number, String description) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(Icons.phone, color: Theme.of(context).colorScheme.primary),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Number: $number'),
            Text(description, style: TextStyle(color: Colors.grey[600])),
          ],
        ),
        trailing: ElevatedButton.icon(
          onPressed: () => _makeCall(context, number),
          icon: const Icon(Icons.call, size: 16),
          label: const Text('Call Now'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        ),
      ),
    );
  }

  Future<void> _launchInCustomTabs(BuildContext context, String url) async {
    try {
      await custom_tabs.launchUrl(
        Uri.parse(url),
        customTabsOptions: custom_tabs.CustomTabsOptions(
          colorSchemes: custom_tabs.CustomTabsColorSchemes.defaults(
            toolbarColor: Theme.of(context).colorScheme.primary,
          ),
          shareState: custom_tabs.CustomTabsShareState.on,
          urlBarHidingEnabled: true,
          showTitle: true,
        ),
        safariVCOptions: custom_tabs.SafariViewControllerOptions(
          preferredBarTintColor: Theme.of(context).colorScheme.primary,
          preferredControlTintColor: Colors.white,
        ),
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open website: $url'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sendSms(BuildContext context, String format, String number) async {
    try {
      final Uri smsUri = Uri(
        scheme: 'sms',
        path: number,
        queryParameters: {'body': format.replaceAll('<symbol no>', 'YOUR_SYMBOL_NUMBER')},
      );
      await launchUrl(smsUri);
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open SMS app'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _makeCall(BuildContext context, String number) async {
    try {
      final Uri callUri = Uri(scheme: 'tel', path: number);
      await launchUrl(callUri);
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open phone dialer'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

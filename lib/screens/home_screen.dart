import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;

import '../providers/app_provider.dart';
import '../widgets/result_service_card.dart';
import '../widgets/ipo_result_card.dart';
import '../screens/books_screen.dart';
import '../screens/tools_screen.dart';
import '../utils/nepali_date_utils.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    const HomeTab(),
    const BooksScreen(),
    const ToolsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Load notifications on app start
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppProvider>().loadNotifications();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Nepali Result All-in-One',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            Text(
              NepaliDateUtils.getCurrentNepaliDate(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          // Dark Mode Toggle
          Consumer<AppProvider>(
            builder: (context, provider, child) {
              return IconButton(
                icon: Icon(
                  provider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                  color: Colors.white,
                ),
                onPressed: () => provider.toggleDarkMode(),
              );
            },
          ),
          // Notification Bell
          Consumer<AppProvider>(
            builder: (context, provider, child) {
              return Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.notifications, color: Colors.white),
                    onPressed: () => Navigator.pushNamed(context, '/notifications'),
                  ),
                  if (provider.unreadNotificationCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '${provider.unreadNotificationCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          // Menu
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: (value) => _handleMenuSelection(context, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'about',
                child: ListTile(
                  leading: Icon(Icons.info),
                  title: Text('About App'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'privacy',
                child: ListTile(
                  leading: Icon(Icons.privacy_tip),
                  title: Text('Privacy Policy'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'terms',
                child: ListTile(
                  leading: Icon(Icons.description),
                  title: Text('Terms & Conditions'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('Share App'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'rate',
                child: ListTile(
                  leading: Icon(Icons.star),
                  title: Text('Rate App'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.book),
            label: 'Books',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.build),
            label: 'Tools',
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(BuildContext context, String value) {
    switch (value) {
      case 'about':
        _showAboutDialog(context);
        break;
      case 'privacy':
        _openUrl('https://technokdapps.blogspot.com/2025/05/privacy-policy-body-font-family-arial.html');
        break;
      case 'terms':
        _openUrl('https://technokdapps.blogspot.com/2025/05/terms-and-conditions-privacy-policy.html');
        break;
      case 'share':
        _shareApp(context);
        break;
      case 'rate':
        _openUrl('https://play.google.com/store');
        break;
    }
  }

  Future<void> _openUrl(String url) async {
    try {
      await url_launcher.launchUrl(
        Uri.parse(url),
        mode: url_launcher.LaunchMode.externalApplication,
      );
    } catch (e) {
      print('Could not launch $url');
    }
  }

  void _shareApp(BuildContext context) {
    // In a real app, you'd use the share_plus package
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality will be available in the Play Store version'),
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About Nepali Result All-in-One'),
        content: const Text(
          'Complete solution for Nepali educational results and student tools.\n\n'
          'Features:\n'
          '• SEE, NEB, TU, CTEVT, KU, PU Results\n'
          '• DV Lottery & IPO Results\n'
          '• Educational Books (Class 10-12)\n'
          '• Student Tools & Calculators\n'
          '• Nepali Date Converter\n'
          '• And much more!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class HomeTab extends StatelessWidget {
  const HomeTab({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Result Services',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            childAspectRatio: 1.2,
            children: const [
              ResultServiceCard(
                title: 'SEE Results',
                subtitle: 'Secondary Education',
                icon: Icons.school,
                url: '',
                smsInstructions: '',
                isInternal: true,
                internalRoute: '/see-results',
              ),
              ResultServiceCard(
                title: 'HSEB Results',
                subtitle: 'Class 11 & 12',
                icon: Icons.grade,
                url: '',
                smsInstructions: '',
                isInternal: true,
                internalRoute: '/hseb-results',
              ),
              ResultServiceCard(
                title: 'TU Results',
                subtitle: 'Tribhuvan University',
                icon: Icons.account_balance,
                url: 'https://tribhuvan-university.edu.np',
                smsInstructions: 'Alternative: https://tuexam.edu.np',
              ),
              ResultServiceCard(
                title: 'CTEVT Results',
                subtitle: 'Technical Education',
                icon: Icons.engineering,
                url: 'https://ctevt.org.np',
                smsInstructions: 'Result Link: https://ctevtexam.org.np',
              ),
              ResultServiceCard(
                title: 'KU Results',
                subtitle: 'Kathmandu University',
                icon: Icons.location_city,
                url: 'https://ku.edu.np',
                smsInstructions: '',
              ),
              ResultServiceCard(
                title: 'PU Results',
                subtitle: 'Purbanchal University',
                icon: Icons.terrain,
                url: 'https://purbuniv.edu.np',
                smsInstructions: '',
              ),
              ResultServiceCard(
                title: 'DV Lottery Results',
                subtitle: 'US Diversity Visa',
                icon: Icons.card_giftcard,
                url: 'https://dvprogram.state.gov',
                smsInstructions: '',
              ),
              IpoResultCard(),
            ],
          ),
        ],
      ),
    );
  }
}

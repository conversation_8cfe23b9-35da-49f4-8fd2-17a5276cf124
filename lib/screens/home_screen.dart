import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../widgets/result_service_card.dart';

import '../screens/books_screen.dart';
import '../screens/tools_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    const HomeTab(),
    const BooksScreen(),
    const ToolsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Load notifications on app start
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppProvider>().loadNotifications();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          children: [
            Text(
              'Nepali Result',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'All-in-One',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white70,
              ),
            ),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: (value) => _handleMenuSelection(context, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'about',
                child: ListTile(
                  leading: Icon(Icons.info),
                  title: Text('About App'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'privacy',
                child: ListTile(
                  leading: Icon(Icons.privacy_tip),
                  title: Text('Privacy Policy'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'terms',
                child: ListTile(
                  leading: Icon(Icons.description),
                  title: Text('Terms & Conditions'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('Share App'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'rate',
                child: ListTile(
                  leading: Icon(Icons.star),
                  title: Text('Rate App'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.book),
            label: 'Books',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.build),
            label: 'Tools',
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(BuildContext context, String value) {
    switch (value) {
      case 'about':
        _showAboutDialog(context);
        break;
      case 'privacy':
        // Open privacy policy URL
        break;
      case 'terms':
        // Open terms and conditions URL
        break;
      case 'share':
        // Share app
        break;
      case 'rate':
        // Open Play Store
        break;
    }
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About Nepali Result All-in-One'),
        content: const Text(
          'Complete solution for Nepali educational results and student tools.\n\n'
          'Features:\n'
          '• SEE, NEB, TU, CTEVT, KU, PU Results\n'
          '• DV Lottery & IPO Results\n'
          '• Educational Books (Class 10-12)\n'
          '• Student Tools & Calculators\n'
          '• Nepali Date Converter\n'
          '• And much more!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class HomeTab extends StatelessWidget {
  const HomeTab({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Result Services',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            childAspectRatio: 1.2,
            children: const [
              ResultServiceCard(
                title: 'SEE (SC)',
                subtitle: 'Secondary Education',
                icon: Icons.school,
                url: 'https://see.gov.np',
                smsInstructions: 'NTC/Ncell: SEE <Symbol No> to 1600',
              ),
              ResultServiceCard(
                title: 'NEB',
                subtitle: 'Class 11 & 12',
                icon: Icons.grade,
                url: 'https://neb.gov.np',
                smsInstructions: 'NTC/Ncell: NEB <Symbol No> to 1600',
              ),
              ResultServiceCard(
                title: 'TU',
                subtitle: 'Tribhuvan University',
                icon: Icons.account_balance,
                url: 'https://tribhuvan-university.edu.np',
                smsInstructions: '',
              ),
              ResultServiceCard(
                title: 'CTEVT',
                subtitle: 'Technical Education',
                icon: Icons.engineering,
                url: 'https://ctevt.org.np',
                smsInstructions: '',
              ),
              ResultServiceCard(
                title: 'KU',
                subtitle: 'Kathmandu University',
                icon: Icons.location_city,
                url: 'https://ku.edu.np',
                smsInstructions: '',
              ),
              ResultServiceCard(
                title: 'PU',
                subtitle: 'Purbanchal University',
                icon: Icons.terrain,
                url: 'https://purbuniv.edu.np',
                smsInstructions: '',
              ),
              ResultServiceCard(
                title: 'DV Lottery',
                subtitle: 'US Diversity Visa',
                icon: Icons.card_giftcard,
                url: 'https://dvprogram.state.gov',
                smsInstructions: '',
              ),
              ResultServiceCard(
                title: 'IPO Result',
                subtitle: 'Share Market',
                icon: Icons.trending_up,
                url: 'https://iporesult.cdsc.com.np',
                smsInstructions: '',
              ),
            ],
          ),
        ],
      ),
    );
  }
}

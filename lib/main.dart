import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'screens/home_screen.dart';
import 'screens/see_results_screen.dart';
import 'screens/hseb_results_screen.dart';
import 'screens/notifications_screen.dart';
import 'providers/app_provider.dart';
import 'utils/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase (optional - can be configured later)
  try {
    await Firebase.initializeApp();
  } catch (e) {
    // Firebase not configured yet, continue without it
    print('Firebase not configured: $e');
  }

  runApp(const NepaliResultApp());
}

class NepaliResultApp extends StatelessWidget {
  const NepaliResultApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AppProvider(),
      child: MaterialApp(
        title: 'Nepali Result All-in-One',
        theme: AppTheme.lightTheme,
        home: const HomeScreen(),
        debugShowCheckedModeBanner: false,
        routes: {
          '/see-results': (context) => const SeeResultsScreen(),
          '/hseb-results': (context) => const HsebResultsScreen(),
          '/notifications': (context) => const NotificationsScreen(),
        },
      ),
    );
  }
}



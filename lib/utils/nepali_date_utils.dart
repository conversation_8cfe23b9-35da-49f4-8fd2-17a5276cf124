import 'package:intl/intl.dart';

class NepaliDateUtils {
  // Nepali months
  static const List<String> nepaliMonths = [
    'बैशाख', 'जेठ', 'असार', 'साउन', 'भदौ', 'असोज',
    'कार्तिक', 'मंसिर', 'पुष', 'माघ', 'फाल्गुन', 'चैत'
  ];

  // Nepali weekdays
  static const List<String> nepaliWeekdays = [
    'आइतबार', 'सोमबार', 'मंगलबार', 'बुधबार', 'बिहिबार', 'शुक्रबार', 'शनिबार'
  ];

  // Nepali numerals
  static const List<String> nepaliNumerals = [
    '०', '१', '२', '३', '४', '५', '६', '७', '८', '९'
  ];

  // Convert English number to Nepali
  static String toNepaliNumber(int number) {
    String englishNumber = number.toString();
    String nepaliNumber = '';
    
    for (int i = 0; i < englishNumber.length; i++) {
      int digit = int.parse(englishNumber[i]);
      nepaliNumber += nepaliNumerals[digit];
    }
    
    return nepaliNumber;
  }

  // Get current Nepali date (simplified conversion)
  static String getCurrentNepaliDate() {
    DateTime now = DateTime.now();
    
    // Simplified BS conversion (approximate)
    // This is a basic conversion - for production, use a proper BS calendar library
    int bsYear = now.year + 57; // Approximate conversion
    int bsMonth = now.month;
    int bsDay = now.day;
    
    // Adjust for Nepali calendar differences
    if (now.month >= 4) {
      bsMonth = now.month - 3;
    } else {
      bsMonth = now.month + 9;
      bsYear--;
    }
    
    // Get weekday
    String weekday = nepaliWeekdays[now.weekday % 7];
    
    // Format the date
    String nepaliYear = toNepaliNumber(bsYear);
    String nepaliMonthName = nepaliMonths[bsMonth - 1];
    String nepaliDayNumber = toNepaliNumber(bsDay);
    
    return '$nepaliYear $nepaliMonthName $nepaliDayNumber, $weekday';
  }

  // Get formatted English date for comparison
  static String getCurrentEnglishDate() {
    DateTime now = DateTime.now();
    return DateFormat('MMMM d, yyyy - EEEE').format(now);
  }
}

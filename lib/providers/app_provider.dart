import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppProvider extends ChangeNotifier {
  bool _isLoading = false;
  bool _isDarkMode = false;
  List<Map<String, dynamic>> _notifications = [];
  
  bool get isLoading => _isLoading;
  bool get isDarkMode => _isDarkMode;
  List<Map<String, dynamic>> get notifications => _notifications;
  
  int get unreadNotificationCount => _notifications.length;

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void toggleDarkMode() {
    _isDarkMode = !_isDarkMode;
    _saveDarkModePreference();
    notifyListeners();
  }

  Future<void> _saveDarkModePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isDarkMode', _isDarkMode);
    } catch (e) {
      print('Error saving dark mode preference: $e');
    }
  }

  void addNotification(String title, String message) {
    final notification = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'title': title,
      'message': message,
      'timestamp': DateTime.now(),
    };
    
    _notifications.insert(0, notification);
    
    // Keep only last 7 days of notifications
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
    _notifications.removeWhere((notification) => 
        notification['timestamp'].isBefore(sevenDaysAgo));
    
    _saveNotifications();
    notifyListeners();
  }

  void removeNotification(String id) {
    _notifications.removeWhere((notification) => notification['id'] == id);
    _saveNotifications();
    notifyListeners();
  }

  void clearAllNotifications() {
    _notifications.clear();
    _saveNotifications();
    notifyListeners();
  }

  Future<void> _saveNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationStrings = _notifications.map((notification) => {
        'id': notification['id'],
        'title': notification['title'],
        'message': notification['message'],
        'timestamp': notification['timestamp'].toIso8601String(),
      }).toList();
      
      await prefs.setString('notifications', notificationStrings.toString());
    } catch (e) {
      print('Error saving notifications: $e');
    }
  }

  Future<void> loadNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isDark = prefs.getBool('isDarkMode') ?? false;
      _isDarkMode = isDark;
      
      // Load notifications from shared preferences
      // This is a simplified version - in production, you'd use proper JSON serialization
      notifyListeners();
    } catch (e) {
      print('Error loading notifications: $e');
    }
  }
}

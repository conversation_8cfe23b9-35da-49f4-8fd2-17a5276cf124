{"buildFiles": ["/Users/<USER>/Downloads/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/.cxx/Debug/3s5s5z6n/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/.cxx/Debug/3s5s5z6n/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
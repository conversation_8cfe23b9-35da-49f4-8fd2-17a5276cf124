{"logs": [{"outputFile": "com.technokd.nepali_result_all_in_one.app-mergeDebugResources-46:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/res/values-en-rGB/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "133", "endOffsets": "332"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4463", "endColumns": "137", "endOffsets": "4596"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0d0ad2c9a7eee0ad2b557032bddebd70/transformed/appcompat-1.1.0/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,870,961,1053,1148,1242,1343,1436,1531,1625,1716,1807,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,75,90,91,94,93,100,92,94,93,90,90,80,102,102,98,104,103,103,155,99,81", "endOffsets": "204,304,412,496,596,711,789,865,956,1048,1143,1237,1338,1431,1526,1620,1711,1802,1883,1986,2089,2188,2293,2397,2501,2657,2757,2839"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,870,961,1053,1148,1242,1343,1436,1531,1625,1716,1807,1888,1991,2094,2193,2298,2402,2506,2662,6406", "endColumns": "103,99,107,83,99,114,77,75,90,91,94,93,100,92,94,93,90,90,80,102,102,98,104,103,103,155,99,81", "endOffsets": "204,304,412,496,596,711,789,865,956,1048,1143,1237,1338,1431,1526,1620,1711,1802,1883,1986,2089,2188,2293,2397,2501,2657,2757,6483"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2762,2858,2960,3059,3158,3262,3365,6488", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "2853,2955,3054,3153,3257,3360,3476,6584"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/res/values-en-rGB/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,446,567,670,817,936,1048,1147,1302,1403,1551,1672,1814,1958,2017,2075", "endColumns": "100,147,120,102,146,118,111,98,154,100,147,120,141,143,58,57,74", "endOffsets": "297,445,566,669,816,935,1047,1146,1301,1402,1550,1671,1813,1957,2016,2074,2149"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3481,3586,3738,3863,3970,4121,4244,4360,4601,4760,4865,5017,5142,5288,5436,5499,5561", "endColumns": "104,151,124,106,150,122,115,102,158,104,151,124,145,147,62,61,78", "endOffsets": "3581,3733,3858,3965,4116,4239,4355,4458,4755,4860,5012,5137,5283,5431,5494,5556,5635"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5640,5806,6198,6272,6589,6758,6838", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "5703,5888,6267,6401,6753,6833,6909"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5708,5893,5990,6099", "endColumns": "97,96,108,98", "endOffsets": "5801,5985,6094,6193"}}]}]}
[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86", "file_": "/Users/<USER>/Downloads/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/.cxx/Debug/3s5s5z6n/x86/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/Downloads/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/Downloads/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]
/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/Downloads/flutter/packages/flutter_tools/gradle/src/main/scripts \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=21 \
  -DANDROID_PLATFORM=android-21 \
  -DANDROID_ABI=arm64-v8a \
  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \
  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/app/intermediates/cxx/Debug/3s5s5z6n/obj/arm64-v8a" \
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/app/intermediates/cxx/Debug/3s5s5z6n/obj/arm64-v8a" \
  -DCMAKE_BUILD_TYPE=Debug \
  "-B/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/.cxx/Debug/3s5s5z6n/arm64-v8a" \
  -GNinja \
  -Wno-dev \
  --no-warn-unused-cli

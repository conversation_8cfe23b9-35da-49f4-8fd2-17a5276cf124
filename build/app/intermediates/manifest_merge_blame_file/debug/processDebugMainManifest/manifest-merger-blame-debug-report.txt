1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.technokd.nepali_result_all_in_one"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:2:5-67
15-->/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:2:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:3:5-79
16-->/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:3:22-76
17    <!--
18 Required to query activities that can process text, see:
19         https://developer.android.com/training/package-visibility and
20         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
21
22         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
23    -->
24    <queries>
24-->/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:42:5-47:15
25        <intent>
25-->/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:43:9-46:18
26            <action android:name="android.intent.action.PROCESS_TEXT" />
26-->/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:44:13-72
26-->/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:44:21-70
27
28            <data android:mimeType="text/plain" />
28-->/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:45:13-50
28-->/Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:45:19-48
29        </intent>
30        <intent>
30-->[io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:8:9-10:18
31            <action android:name="android.support.customtabs.action.CustomTabsService" />
31-->[io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:9:13-90
31-->[io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:9:21-87
32        </intent>
33    </queries>
34
35    <uses-permission android:name="android.permission.VIBRATE" />
35-->[:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-66
35-->[:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-63
36    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
36-->[:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-77
36-->[:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-74
37    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
37-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-68
37-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-65
38    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
38-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:26:5-82
38-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:26:22-79
39
40    <permission
40-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
41        android:name="com.technokd.nepali_result_all_in_one.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.technokd.nepali_result_all_in_one.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
45
46    <application
47        android:name="android.app.Application"
48        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
48-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
49        android:debuggable="true"
50        android:extractNativeLibs="true"
51        android:icon="@mipmap/ic_launcher"
52        android:label="Nepali Result All-in-One" >
53        <activity
54            android:name="com.technokd.nepali_result_all_in_one.MainActivity"
55            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
56            android:exported="true"
57            android:hardwareAccelerated="true"
58            android:launchMode="singleTop"
59            android:taskAffinity=""
60            android:theme="@style/LaunchTheme"
61            android:windowSoftInputMode="adjustResize" >
62
63            <!--
64                 Specifies an Android theme to apply to this Activity as soon as
65                 the Android process has started. This theme is visible to the user
66                 while the Flutter UI initializes. After that, this theme continues
67                 to determine the Window background behind the Flutter UI.
68            -->
69            <meta-data
70                android:name="io.flutter.embedding.android.NormalTheme"
71                android:resource="@style/NormalTheme" />
72
73            <intent-filter>
74                <action android:name="android.intent.action.MAIN" />
75
76                <category android:name="android.intent.category.LAUNCHER" />
77            </intent-filter>
78        </activity>
79        <!--
80             Don't delete the meta-data below.
81             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
82        -->
83        <meta-data
84            android:name="flutterEmbedding"
85            android:value="2" />
86
87        <activity
87-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
88            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
88-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
89            android:exported="false"
89-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
90            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
90-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
91
92        <service
92-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-17:72
93            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
93-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-107
94            android:exported="false"
94-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
95            android:permission="android.permission.BIND_JOB_SERVICE" />
95-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-69
96        <service
96-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:9-24:19
97            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
97-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:13-97
98            android:exported="false" >
98-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-37
99            <intent-filter>
99-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-23:29
100                <action android:name="com.google.firebase.MESSAGING_EVENT" />
100-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:17-78
100-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:25-75
101            </intent-filter>
102        </service>
103
104        <receiver
104-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-33:20
105            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
105-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-98
106            android:exported="true"
106-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-36
107            android:permission="com.google.android.c2dm.permission.SEND" >
107-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-73
108            <intent-filter>
108-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-32:29
109                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
109-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:17-81
109-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:25-78
110            </intent-filter>
111        </receiver>
112
113        <service
113-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:9-39:19
114            android:name="com.google.firebase.components.ComponentDiscoveryService"
114-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:18-89
115            android:directBootAware="true"
115-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:32:13-43
116            android:exported="false" >
116-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:56:13-37
117            <meta-data
117-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-38:85
118                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
118-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:17-128
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:17-82
120            <meta-data
120-->[:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
121                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
121-->[:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
123            <meta-data
123-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:57:13-59:85
124                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
124-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:58:17-122
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:59:17-82
126            <meta-data
126-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:60:13-62:85
127                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
127-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:61:17-119
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:62:17-82
129            <meta-data
129-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
130                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
130-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
132            <meta-data
132-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
133                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
133-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
135            <meta-data
135-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
136                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
136-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
138            <meta-data
138-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
139                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
139-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:36:17-109
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:37:17-82
141            <meta-data
141-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
142                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
142-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
144        </service>
145
146        <provider
146-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:41:9-45:38
147            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
147-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:42:13-102
148            android:authorities="com.technokd.nepali_result_all_in_one.flutterfirebasemessaginginitprovider"
148-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:43:13-88
149            android:exported="false"
149-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:44:13-37
150            android:initOrder="99" />
150-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:45:13-35
151
152        <uses-library
152-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
153            android:name="androidx.window.extensions"
153-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
154            android:required="false" />
154-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
155        <uses-library
155-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
156            android:name="androidx.window.sidecar"
156-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
157            android:required="false" />
157-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
158
159        <receiver
159-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:29:9-40:20
160            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
160-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:30:13-78
161            android:exported="true"
161-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:31:13-36
162            android:permission="com.google.android.c2dm.permission.SEND" >
162-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:32:13-73
163            <intent-filter>
163-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-32:29
164                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
164-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:17-81
164-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:25-78
165            </intent-filter>
166
167            <meta-data
167-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:37:13-39:40
168                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
168-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:38:17-92
169                android:value="true" />
169-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:39:17-37
170        </receiver>
171        <!--
172             FirebaseMessagingService performs security checks at runtime,
173             but set to not exported to explicitly avoid allowing another app to call it.
174        -->
175        <service
175-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:46:9-53:19
176            android:name="com.google.firebase.messaging.FirebaseMessagingService"
176-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:47:13-82
177            android:directBootAware="true"
177-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:48:13-43
178            android:exported="false" >
178-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:49:13-37
179            <intent-filter android:priority="-500" >
179-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-23:29
180                <action android:name="com.google.firebase.MESSAGING_EVENT" />
180-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:17-78
180-->[:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:25-75
181            </intent-filter>
182        </service>
183
184        <provider
184-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
185            android:name="com.google.firebase.provider.FirebaseInitProvider"
185-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:24:13-77
186            android:authorities="com.technokd.nepali_result_all_in_one.firebaseinitprovider"
186-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:25:13-72
187            android:directBootAware="true"
187-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:26:13-43
188            android:exported="false"
188-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:27:13-37
189            android:initOrder="100" />
189-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:28:13-36
190
191        <activity
191-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:9-22:45
192            android:name="com.google.android.gms.common.api.GoogleApiActivity"
192-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:19-85
193            android:exported="false"
193-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:22:19-43
194            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
194-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:21:19-78
195
196        <provider
196-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
197            android:name="androidx.startup.InitializationProvider"
197-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
198            android:authorities="com.technokd.nepali_result_all_in_one.androidx-startup"
198-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
199            android:exported="false" >
199-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
200            <meta-data
200-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
201                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
201-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
202                android:value="androidx.startup" />
202-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
203            <meta-data
203-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
204                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
204-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
205                android:value="androidx.startup" />
205-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
206        </provider>
207
208        <meta-data
208-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
209            android:name="com.google.android.gms.version"
209-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
210            android:value="@integer/google_play_services_version" />
210-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
211
212        <receiver
212-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
213            android:name="androidx.profileinstaller.ProfileInstallReceiver"
213-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
214            android:directBootAware="false"
214-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
215            android:enabled="true"
215-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
216            android:exported="true"
216-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
217            android:permission="android.permission.DUMP" >
217-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
218            <intent-filter>
218-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
219                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
219-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
219-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
220            </intent-filter>
221            <intent-filter>
221-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
222                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
222-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
222-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
223            </intent-filter>
224            <intent-filter>
224-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
225                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
225-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
225-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
226            </intent-filter>
227            <intent-filter>
227-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
228                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
228-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
228-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
229            </intent-filter>
230        </receiver>
231
232        <service
232-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
233            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
233-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
234            android:exported="false" >
234-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
235            <meta-data
235-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
236                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
236-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
237                android:value="cct" />
237-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
238        </service>
239        <service
239-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
240            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
240-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
241            android:exported="false"
241-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
242            android:permission="android.permission.BIND_JOB_SERVICE" >
242-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
243        </service>
244
245        <receiver
245-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
246            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
246-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
247            android:exported="false" />
247-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
248    </application>
249
250</manifest>

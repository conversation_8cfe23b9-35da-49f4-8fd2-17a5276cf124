-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:5:5-36:19
INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml
MERGED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/244ebfbd542d32fc159ad1c2e863258a/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/244ebfbd542d32fc159ad1c2e863258a/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/500a0c9cb71e092f235465c1951cf452/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/500a0c9cb71e092f235465c1951cf452/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/c911b2bb9ac96af621f362c1e94d1d78/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/c911b2bb9ac96af621f362c1e94d1d78/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c1410397b3c38d45fcfb6f835e622ed9/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c1410397b3c38d45fcfb6f835e622ed9/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:1:1-48:12
MERGED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:1:1-48:12
INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:flutter_custom_tabs_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_custom_tabs_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-10:12
MERGED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-14:12
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-15:12
MERGED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:2:1-13:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/f84db7003533a22de0405c5251ecb704/transformed/media-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/79275990ee9dddfd68bc7c9d7157e0cd/transformed/recyclerview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/984bdeb02044daf662dc2d3e1fe07483/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/f71e40716bc29995f4cada24da499d83/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/0d0ad2c9a7eee0ad2b557032bddebd70/transformed/appcompat-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/244ebfbd542d32fc159ad1c2e863258a/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/2b90cb9e6e291107f93471260cd339b4/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/972419750b36e9fbf2d0c26a45927d82/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/84a8a15495959e34577fc85968c66982/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/e60d7cc8f585e105683d15c0883739b4/transformed/jetified-activity-1.8.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/500a0c9cb71e092f235465c1951cf452/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d21df4d1a80ec9bf2502ed8e05d37297/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c244a6ce50b3288fe79d3f6ae212397f/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/306016bcb4195b3238dbb4d76cafb64c/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/425b3275685a974b685af27ff4ed6b1d/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/31eccd218f5b1fd8272959453f411784/transformed/jetified-datastore-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/7535a935f9e65beb6c79d36312378a64/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/479b3bf32f00901a230d7d79262001b9/transformed/jetified-datastore-release/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/c911b2bb9ac96af621f362c1e94d1d78/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c1410397b3c38d45fcfb6f835e622ed9/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e93556932885008eff7df21847fbdad2/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10acfa95459151f0abcb0437238b9ca7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/08d6944c906bcd30c9d42a63993176cf/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/f87704cc6ac259b753f491455f413615/transformed/transition-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/afec9dc0bcc11d087323dc11f5e0350a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/ee817ed912dd87a9ffe7b0d8087b9e11/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/ccda8ddd57f5a835df89427c6970b69a/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106b34a8e64882148068274b889c0b9f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/25a37e5cf0f4e5220cbf7cafe9249990/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7058885b6dd2982185c832c670598b5a/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/277474532995b9fd32fdd0e838dc6db6/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1722168e26811a3eeccb6d84beb26e7b/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/4e0205039249695c798c4522a2d05847/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4cfa7aabd0ff8beb21daa4d12f46b519/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a58c138301656e62a00a9163f21e3a54/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b7f63da0fad92ba134922d35b82d48c3/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:2:5-67
MERGED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:2:5-67
MERGED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:2:5-67
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:2:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:3:5-79
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-79
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:3:22-76
queries
ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:42:5-47:15
MERGED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:7:5-11:15
MERGED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:7:5-11:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:43:9-46:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:44:13-72
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:44:21-70
data
ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:45:13-50
	android:mimeType
		ADDED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/main/AndroidManifest.xml:45:19-48
uses-sdk
INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml
MERGED from [:flutter_custom_tabs_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_custom_tabs_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_custom_tabs_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_custom_tabs_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:5:5-44
MERGED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/f84db7003533a22de0405c5251ecb704/transformed/media-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/f84db7003533a22de0405c5251ecb704/transformed/media-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/79275990ee9dddfd68bc7c9d7157e0cd/transformed/recyclerview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/79275990ee9dddfd68bc7c9d7157e0cd/transformed/recyclerview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/984bdeb02044daf662dc2d3e1fe07483/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/984bdeb02044daf662dc2d3e1fe07483/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/f71e40716bc29995f4cada24da499d83/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/f71e40716bc29995f4cada24da499d83/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/0d0ad2c9a7eee0ad2b557032bddebd70/transformed/appcompat-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/0d0ad2c9a7eee0ad2b557032bddebd70/transformed/appcompat-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/244ebfbd542d32fc159ad1c2e863258a/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/244ebfbd542d32fc159ad1c2e863258a/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/2b90cb9e6e291107f93471260cd339b4/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/2b90cb9e6e291107f93471260cd339b4/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/972419750b36e9fbf2d0c26a45927d82/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/972419750b36e9fbf2d0c26a45927d82/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/84a8a15495959e34577fc85968c66982/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/84a8a15495959e34577fc85968c66982/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/e60d7cc8f585e105683d15c0883739b4/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/e60d7cc8f585e105683d15c0883739b4/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/500a0c9cb71e092f235465c1951cf452/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/500a0c9cb71e092f235465c1951cf452/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d21df4d1a80ec9bf2502ed8e05d37297/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d21df4d1a80ec9bf2502ed8e05d37297/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c244a6ce50b3288fe79d3f6ae212397f/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c244a6ce50b3288fe79d3f6ae212397f/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/306016bcb4195b3238dbb4d76cafb64c/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/306016bcb4195b3238dbb4d76cafb64c/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/425b3275685a974b685af27ff4ed6b1d/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/425b3275685a974b685af27ff4ed6b1d/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/31eccd218f5b1fd8272959453f411784/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/31eccd218f5b1fd8272959453f411784/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/7535a935f9e65beb6c79d36312378a64/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/7535a935f9e65beb6c79d36312378a64/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/479b3bf32f00901a230d7d79262001b9/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/479b3bf32f00901a230d7d79262001b9/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/c911b2bb9ac96af621f362c1e94d1d78/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/c911b2bb9ac96af621f362c1e94d1d78/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c1410397b3c38d45fcfb6f835e622ed9/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c1410397b3c38d45fcfb6f835e622ed9/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e93556932885008eff7df21847fbdad2/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e93556932885008eff7df21847fbdad2/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10acfa95459151f0abcb0437238b9ca7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10acfa95459151f0abcb0437238b9ca7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/08d6944c906bcd30c9d42a63993176cf/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/08d6944c906bcd30c9d42a63993176cf/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/f87704cc6ac259b753f491455f413615/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/f87704cc6ac259b753f491455f413615/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/afec9dc0bcc11d087323dc11f5e0350a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/afec9dc0bcc11d087323dc11f5e0350a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/ee817ed912dd87a9ffe7b0d8087b9e11/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/ee817ed912dd87a9ffe7b0d8087b9e11/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/ccda8ddd57f5a835df89427c6970b69a/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/ccda8ddd57f5a835df89427c6970b69a/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106b34a8e64882148068274b889c0b9f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106b34a8e64882148068274b889c0b9f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/25a37e5cf0f4e5220cbf7cafe9249990/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/25a37e5cf0f4e5220cbf7cafe9249990/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7058885b6dd2982185c832c670598b5a/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7058885b6dd2982185c832c670598b5a/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/277474532995b9fd32fdd0e838dc6db6/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/277474532995b9fd32fdd0e838dc6db6/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1722168e26811a3eeccb6d84beb26e7b/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1722168e26811a3eeccb6d84beb26e7b/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/4e0205039249695c798c4522a2d05847/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/4e0205039249695c798c4522a2d05847/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4cfa7aabd0ff8beb21daa4d12f46b519/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4cfa7aabd0ff8beb21daa4d12f46b519/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a58c138301656e62a00a9163f21e3a54/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a58c138301656e62a00a9163f21e3a54/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b7f63da0fad92ba134922d35b82d48c3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b7f63da0fad92ba134922d35b82d48c3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/android/app/src/debug/AndroidManifest.xml
uses-permission#android.permission.VIBRATE
ADDED from [:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-63
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-77
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-77
MERGED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:flutter_local_notifications] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-74
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-65
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:25-78
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] /Users/<USER>/Documents/augment-projects/Nepali Result All-in-One/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:8:9-10:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:9:13-90
	android:name
		ADDED from [io.github.droibit:customtabslauncher:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/321b3b56550d87e9e2292d63f37a338a/transformed/jetified-customtabslauncher-3.0.0/AndroidManifest.xml:9:21-87
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/8402a43808227de1a42b20b4957f5701/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e4623bee9755d77b485c2395b1cb13f4/transformed/jetified-firebase-messaging-24.1.1/AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.technokd.nepali_result_all_in_one.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.technokd.nepali_result_all_in_one.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2cd4ba1245dada71f7874fd5d5bf7af2/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/4dd55bdee0a4e18f01a67f197e6c9ccf/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/a93e75e64be64d9ba356bb6c65cbb971/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:33:13-132

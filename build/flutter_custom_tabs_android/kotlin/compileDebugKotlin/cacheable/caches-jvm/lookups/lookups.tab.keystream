  SuppressLint android.annotation  Activity android.app  ActivityManager android.app  getSystemService android.app.Activity  	javaClass android.app.Activity  packageManager android.app.Activity  
startActivity android.app.Activity  startActivityForResult android.app.Activity  appTasks android.app.ActivityManager  taskInfo #android.app.ActivityManager.AppTask  baseActivity android.app.TaskInfo  topActivity android.app.TaskInfo  ActivityNotFoundException android.content  
ComponentName android.content  Context android.content  Intent android.content  message )android.content.ActivityNotFoundException  packageName android.content.ComponentName  getSystemService android.content.Context  packageManager android.content.Context  packageName android.content.Context  	resources android.content.Context  
startActivity android.content.Context  
unbindService android.content.Context  packageManager android.content.ContextWrapper  ACTION_VIEW android.content.Intent  CATEGORY_BROWSABLE android.content.Intent  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  !FLAG_ACTIVITY_REQUIRE_NON_BROWSER android.content.Intent  FLAG_ACTIVITY_SINGLE_TOP android.content.Intent  addCategory android.content.Intent  addFlags android.content.Intent  
getPackage android.content.Intent  hasExtra android.content.Intent  putExtra android.content.Intent  	setAction android.content.Intent  setData android.content.Intent  setFlags android.content.Intent  
setPackage android.content.Intent  PackageManager android.content.pm  ResolveInfo android.content.pm  packageName "android.content.pm.PackageItemInfo  	MATCH_ALL !android.content.pm.PackageManager  MATCH_DEFAULT_ONLY !android.content.pm.PackageManager  ResolveInfoFlags !android.content.pm.PackageManager  queryIntentActivities !android.content.pm.PackageManager  resolveService !android.content.pm.PackageManager  of 2android.content.pm.PackageManager.ResolveInfoFlags  activityInfo android.content.pm.ResolveInfo  displayMetrics android.content.res.Resources  
getIdentifier android.content.res.Resources  Bitmap android.graphics  Color android.graphics  
parseColor android.graphics.Color  Drawable android.graphics.drawable  mutate "android.graphics.drawable.Drawable  toBitmap "android.graphics.drawable.Drawable  Uri android.net  	fromParts android.net.Uri  scheme android.net.Uri  Build 
android.os  Bundle 
android.os  	putString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  LOLLIPOP android.os.Build.VERSION_CODES  M android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  apply android.os.Bundle  
component1 android.os.Bundle  
component2 android.os.Bundle  iterator android.os.Bundle  	putString android.os.Bundle  
EXTRA_HEADERS android.provider.Browser  Log android.util  density android.util.DisplayMetrics  getStackTraceString android.util.Log  AnimRes androidx.annotation  AnyRes androidx.annotation  	Dimension androidx.annotation  Px androidx.annotation  RequiresApi androidx.annotation  
RestrictTo androidx.annotation  VisibleForTesting androidx.annotation  	Companion androidx.annotation.Dimension  DP androidx.annotation.Dimension  DP 'androidx.annotation.Dimension.Companion  Scope androidx.annotation.RestrictTo  LIBRARY $androidx.annotation.RestrictTo.Scope  CustomTabColorSchemeParams androidx.browser.customtabs  CustomTabsClient androidx.browser.customtabs  CustomTabsIntent androidx.browser.customtabs  CustomTabsServiceConnection androidx.browser.customtabs  CustomTabsSession androidx.browser.customtabs  Builder 6androidx.browser.customtabs.CustomTabColorSchemeParams  let 6androidx.browser.customtabs.CustomTabColorSchemeParams  build >androidx.browser.customtabs.CustomTabColorSchemeParams.Builder  setNavigationBarColor >androidx.browser.customtabs.CustomTabColorSchemeParams.Builder  setNavigationBarDividerColor >androidx.browser.customtabs.CustomTabColorSchemeParams.Builder  setToolbarColor >androidx.browser.customtabs.CustomTabColorSchemeParams.Builder  bindCustomTabsService ,androidx.browser.customtabs.CustomTabsClient  
newSession ,androidx.browser.customtabs.CustomTabsClient  warmup ,androidx.browser.customtabs.CustomTabsClient  ActivityHeightResizeBehavior ,androidx.browser.customtabs.CustomTabsIntent  ActivitySideSheetDecorationType ,androidx.browser.customtabs.CustomTabsIntent  ActivitySideSheetPosition ,androidx.browser.customtabs.CustomTabsIntent  'ActivitySideSheetRoundedCornersPosition ,androidx.browser.customtabs.CustomTabsIntent  Builder ,androidx.browser.customtabs.CustomTabsIntent  COLOR_SCHEME_DARK ,androidx.browser.customtabs.CustomTabsIntent  COLOR_SCHEME_LIGHT ,androidx.browser.customtabs.CustomTabsIntent  CloseButtonPosition ,androidx.browser.customtabs.CustomTabsIntent  ColorScheme ,androidx.browser.customtabs.CustomTabsIntent   EXTRA_INITIAL_ACTIVITY_HEIGHT_PX ,androidx.browser.customtabs.CustomTabsIntent  EXTRA_INITIAL_ACTIVITY_WIDTH_PX ,androidx.browser.customtabs.CustomTabsIntent  
ShareState ,androidx.browser.customtabs.CustomTabsIntent  apply ,androidx.browser.customtabs.CustomTabsIntent  applyBrowserConfiguration ,androidx.browser.customtabs.CustomTabsIntent  intent ,androidx.browser.customtabs.CustomTabsIntent  	launchUrl ,androidx.browser.customtabs.CustomTabsIntent  setChromeCustomTabsPackage ,androidx.browser.customtabs.CustomTabsIntent  setCustomTabsPackage ,androidx.browser.customtabs.CustomTabsIntent  build 4androidx.browser.customtabs.CustomTabsIntent.Builder   setActivitySideSheetBreakpointDp 4androidx.browser.customtabs.CustomTabsIntent.Builder  "setActivitySideSheetDecorationType 4androidx.browser.customtabs.CustomTabsIntent.Builder  'setActivitySideSheetMaximizationEnabled 4androidx.browser.customtabs.CustomTabsIntent.Builder  setActivitySideSheetPosition 4androidx.browser.customtabs.CustomTabsIntent.Builder  *setActivitySideSheetRoundedCornersPosition 4androidx.browser.customtabs.CustomTabsIntent.Builder  setBackgroundInteractionEnabled 4androidx.browser.customtabs.CustomTabsIntent.Builder  setBookmarksButtonEnabled 4androidx.browser.customtabs.CustomTabsIntent.Builder  setCloseButtonIcon 4androidx.browser.customtabs.CustomTabsIntent.Builder  setCloseButtonPosition 4androidx.browser.customtabs.CustomTabsIntent.Builder  setColorScheme 4androidx.browser.customtabs.CustomTabsIntent.Builder  setColorSchemeParams 4androidx.browser.customtabs.CustomTabsIntent.Builder  setDefaultColorSchemeParams 4androidx.browser.customtabs.CustomTabsIntent.Builder  setDownloadButtonEnabled 4androidx.browser.customtabs.CustomTabsIntent.Builder  setExitAnimations 4androidx.browser.customtabs.CustomTabsIntent.Builder  setInitialActivityHeightPx 4androidx.browser.customtabs.CustomTabsIntent.Builder  setInitialActivityWidthPx 4androidx.browser.customtabs.CustomTabsIntent.Builder  setInstantAppsEnabled 4androidx.browser.customtabs.CustomTabsIntent.Builder  setShareIdentityEnabled 4androidx.browser.customtabs.CustomTabsIntent.Builder  
setShareState 4androidx.browser.customtabs.CustomTabsIntent.Builder  setShowTitle 4androidx.browser.customtabs.CustomTabsIntent.Builder  setStartAnimations 4androidx.browser.customtabs.CustomTabsIntent.Builder  setToolbarCornerRadiusDp 4androidx.browser.customtabs.CustomTabsIntent.Builder  setUrlBarHidingEnabled 4androidx.browser.customtabs.CustomTabsIntent.Builder  ACTION_CUSTOM_TABS_CONNECTION -androidx.browser.customtabs.CustomTabsService  KEY_URL -androidx.browser.customtabs.CustomTabsService  CustomTabsClient 7androidx.browser.customtabs.CustomTabsServiceConnection  KEY_URL 7androidx.browser.customtabs.CustomTabsServiceConnection  Log 7androidx.browser.customtabs.CustomTabsServiceConnection  SecurityException 7androidx.browser.customtabs.CustomTabsServiceConnection  TAG 7androidx.browser.customtabs.CustomTabsServiceConnection  bundleOf 7androidx.browser.customtabs.CustomTabsServiceConnection  map 7androidx.browser.customtabs.CustomTabsServiceConnection  to 7androidx.browser.customtabs.CustomTabsServiceConnection  toUri 7androidx.browser.customtabs.CustomTabsServiceConnection  mayLaunchUrl -androidx.browser.customtabs.CustomTabsSession  
ContextCompat androidx.core.content  getSystemService androidx.core.content  getDrawable #androidx.core.content.ContextCompat  ResourcesCompat androidx.core.content.res  ID_NULL )androidx.core.content.res.ResourcesCompat  DrawableCompat androidx.core.graphics.drawable  toBitmap androidx.core.graphics.drawable  wrap .androidx.core.graphics.drawable.DrawableCompat  toUri androidx.core.net  bundleOf androidx.core.os  CustomTabsPackageProvider 'com.droibit.android.customtabs.launcher  NonChromeCustomTabs 'com.droibit.android.customtabs.launcher  getCustomTabsPackage 'com.droibit.android.customtabs.launcher  setChromeCustomTabsPackage 'com.droibit.android.customtabs.launcher  setCustomTabsPackage 'com.droibit.android.customtabs.launcher  ACTION_CUSTOM_TABS_CONNECTION -com.github.droibit.flutter.plugins.customtabs  Activity -com.github.droibit.flutter.plugins.customtabs  
ActivityAware -com.github.droibit.flutter.plugins.customtabs  ActivityManager -com.github.droibit.flutter.plugins.customtabs  ActivityNotFoundException -com.github.droibit.flutter.plugins.customtabs  ActivityPluginBinding -com.github.droibit.flutter.plugins.customtabs  Any -com.github.droibit.flutter.plugins.customtabs  BasicMessageChannel -com.github.droibit.flutter.plugins.customtabs  BinaryMessenger -com.github.droibit.flutter.plugins.customtabs  Boolean -com.github.droibit.flutter.plugins.customtabs  Build -com.github.droibit.flutter.plugins.customtabs  Byte -com.github.droibit.flutter.plugins.customtabs  ByteArrayOutputStream -com.github.droibit.flutter.plugins.customtabs  
ByteBuffer -com.github.droibit.flutter.plugins.customtabs  CODE_LAUNCH_ERROR -com.github.droibit.flutter.plugins.customtabs  
ComponentName -com.github.droibit.flutter.plugins.customtabs  
CustomTabsApi -com.github.droibit.flutter.plugins.customtabs  CustomTabsIntentFactory -com.github.droibit.flutter.plugins.customtabs  CustomTabsLauncher -com.github.droibit.flutter.plugins.customtabs  CustomTabsPlugin -com.github.droibit.flutter.plugins.customtabs  CustomTabsSessionManager -com.github.droibit.flutter.plugins.customtabs  ExternalBrowserLauncher -com.github.droibit.flutter.plugins.customtabs  FLAG_ACTIVITY_CLEAR_TOP -com.github.droibit.flutter.plugins.customtabs  FLAG_ACTIVITY_SINGLE_TOP -com.github.droibit.flutter.plugins.customtabs  FlutterError -com.github.droibit.flutter.plugins.customtabs  
FlutterPlugin -com.github.droibit.flutter.plugins.customtabs  FlutterPluginBinding -com.github.droibit.flutter.plugins.customtabs  Int -com.github.droibit.flutter.plugins.customtabs  Intent -com.github.droibit.flutter.plugins.customtabs  JvmOverloads -com.github.droibit.flutter.plugins.customtabs  List -com.github.droibit.flutter.plugins.customtabs  Log -com.github.droibit.flutter.plugins.customtabs  Map -com.github.droibit.flutter.plugins.customtabs  MessageCodec -com.github.droibit.flutter.plugins.customtabs  MessagesPigeonCodec -com.github.droibit.flutter.plugins.customtabs  NativeAppLauncher -com.github.droibit.flutter.plugins.customtabs  PackageManager -com.github.droibit.flutter.plugins.customtabs  PartialCustomTabsLauncher -com.github.droibit.flutter.plugins.customtabs  ResolveInfo -com.github.droibit.flutter.plugins.customtabs  
RestrictTo -com.github.droibit.flutter.plugins.customtabs  StandardMessageCodec -com.github.droibit.flutter.plugins.customtabs  String -com.github.droibit.flutter.plugins.customtabs  Suppress -com.github.droibit.flutter.plugins.customtabs  	Throwable -com.github.droibit.flutter.plugins.customtabs  VisibleForTesting -com.github.droibit.flutter.plugins.customtabs  getSystemService -com.github.droibit.flutter.plugins.customtabs  getValue -com.github.droibit.flutter.plugins.customtabs  
isNotEmpty -com.github.droibit.flutter.plugins.customtabs  	javaClass -com.github.droibit.flutter.plugins.customtabs  lazy -com.github.droibit.flutter.plugins.customtabs  listOf -com.github.droibit.flutter.plugins.customtabs  provideDelegate -com.github.droibit.flutter.plugins.customtabs  requireNotNull -com.github.droibit.flutter.plugins.customtabs  run -com.github.droibit.flutter.plugins.customtabs  setUp -com.github.droibit.flutter.plugins.customtabs  toUri -com.github.droibit.flutter.plugins.customtabs  	wrapError -com.github.droibit.flutter.plugins.customtabs  
wrapResult -com.github.droibit.flutter.plugins.customtabs  Any ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  BasicMessageChannel ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  BinaryMessenger ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  Boolean ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  	Companion ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  
CustomTabsApi ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  JvmOverloads ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  List ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  Map ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  MessageCodec ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  MessagesPigeonCodec ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  String ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  	Throwable ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  closeAllIfPossible ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  codec ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  getValue ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  
invalidate ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  
isNotEmpty ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  launch ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  lazy ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  listOf ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  	mayLaunch ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  provideDelegate ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  run ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  setUp ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  warmup ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  	wrapError ;com.github.droibit.flutter.plugins.customtabs.CustomTabsApi  BasicMessageChannel Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  MessagesPigeonCodec Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  codec Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  getValue Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  
isNotEmpty Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  lazy Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  listOf Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  provideDelegate Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  run Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  setUp Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  	wrapError Ecom.github.droibit.flutter.plugins.customtabs.CustomTabsApi.Companion  ACTION_CUSTOM_TABS_CONNECTION @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  Activity @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  ActivityManager @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  ActivityNotFoundException @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  Any @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  Boolean @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  Build @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  CODE_LAUNCH_ERROR @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  
ComponentName @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  CustomTabsIntentFactory @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  CustomTabsSessionManager @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  ExternalBrowserLauncher @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  FLAG_ACTIVITY_CLEAR_TOP @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  FLAG_ACTIVITY_SINGLE_TOP @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  FlutterError @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  Int @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  Intent @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  List @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  Map @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  NativeAppLauncher @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  PackageManager @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  PartialCustomTabsLauncher @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  ResolveInfo @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  String @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  Suppress @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  VisibleForTesting @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  activity @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  customTabsIntentFactory @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  customTabsSessionManager @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  externalBrowserLauncher @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  getSystemService @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  	javaClass @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  nativeAppLauncher @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  partialCustomTabsLauncher @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  requireNotNull @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  resolveService @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  setActivity @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  toUri @com.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher  ACTION_CUSTOM_TABS_CONNECTION Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  Build Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  CODE_LAUNCH_ERROR Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  
ComponentName Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  CustomTabsIntentFactory Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  CustomTabsSessionManager Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  ExternalBrowserLauncher Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  FLAG_ACTIVITY_CLEAR_TOP Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  FLAG_ACTIVITY_SINGLE_TOP Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  FlutterError Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  Intent Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  NativeAppLauncher Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  PackageManager Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  PartialCustomTabsLauncher Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  getSystemService Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  	javaClass Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  requireNotNull Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  toUri Jcom.github.droibit.flutter.plugins.customtabs.CustomTabsLauncher.Companion  
CustomTabsApi >com.github.droibit.flutter.plugins.customtabs.CustomTabsPlugin  CustomTabsLauncher >com.github.droibit.flutter.plugins.customtabs.CustomTabsPlugin  api >com.github.droibit.flutter.plugins.customtabs.CustomTabsPlugin  onAttachedToActivity >com.github.droibit.flutter.plugins.customtabs.CustomTabsPlugin  onDetachedFromActivity >com.github.droibit.flutter.plugins.customtabs.CustomTabsPlugin  setUp >com.github.droibit.flutter.plugins.customtabs.CustomTabsPlugin  code :com.github.droibit.flutter.plugins.customtabs.FlutterError  details :com.github.droibit.flutter.plugins.customtabs.FlutterError  message :com.github.droibit.flutter.plugins.customtabs.FlutterError  Activity 2com.github.droibit.flutter.plugins.customtabs.core  ActivityNotFoundException 2com.github.droibit.flutter.plugins.customtabs.core  AnimRes 2com.github.droibit.flutter.plugins.customtabs.core  Any 2com.github.droibit.flutter.plugins.customtabs.core  AnyRes 2com.github.droibit.flutter.plugins.customtabs.core  Bitmap 2com.github.droibit.flutter.plugins.customtabs.core  Boolean 2com.github.droibit.flutter.plugins.customtabs.core  BrowserConfiguration 2com.github.droibit.flutter.plugins.customtabs.core  Build 2com.github.droibit.flutter.plugins.customtabs.core  COLOR_SCHEME_DARK 2com.github.droibit.flutter.plugins.customtabs.core  COLOR_SCHEME_LIGHT 2com.github.droibit.flutter.plugins.customtabs.core  Context 2com.github.droibit.flutter.plugins.customtabs.core  
ContextCompat 2com.github.droibit.flutter.plugins.customtabs.core  CustomTabsAnimations 2com.github.droibit.flutter.plugins.customtabs.core  CustomTabsCloseButton 2com.github.droibit.flutter.plugins.customtabs.core  CustomTabsColorSchemes 2com.github.droibit.flutter.plugins.customtabs.core  CustomTabsIntent 2com.github.droibit.flutter.plugins.customtabs.core  CustomTabsIntentFactory 2com.github.droibit.flutter.plugins.customtabs.core  CustomTabsIntentOptions 2com.github.droibit.flutter.plugins.customtabs.core  CustomTabsSessionProvider 2com.github.droibit.flutter.plugins.customtabs.core  Double 2com.github.droibit.flutter.plugins.customtabs.core  DrawableCompat 2com.github.droibit.flutter.plugins.customtabs.core  
EXTRA_HEADERS 2com.github.droibit.flutter.plugins.customtabs.core   EXTRA_INITIAL_ACTIVITY_HEIGHT_PX 2com.github.droibit.flutter.plugins.customtabs.core  EXTRA_INITIAL_ACTIVITY_WIDTH_PX 2com.github.droibit.flutter.plugins.customtabs.core  ExternalBrowserLauncher 2com.github.droibit.flutter.plugins.customtabs.core  ID_NULL 2com.github.droibit.flutter.plugins.customtabs.core  Int 2com.github.droibit.flutter.plugins.customtabs.core  Intent 2com.github.droibit.flutter.plugins.customtabs.core  List 2com.github.droibit.flutter.plugins.customtabs.core  Map 2com.github.droibit.flutter.plugins.customtabs.core  NativeAppLauncher 2com.github.droibit.flutter.plugins.customtabs.core  PackageManager 2com.github.droibit.flutter.plugins.customtabs.core  PartialCustomTabsConfiguration 2com.github.droibit.flutter.plugins.customtabs.core  PartialCustomTabsLauncher 2com.github.droibit.flutter.plugins.customtabs.core  Px 2com.github.droibit.flutter.plugins.customtabs.core   REQUEST_CODE_PARTIAL_CUSTOM_TABS 2com.github.droibit.flutter.plugins.customtabs.core  RequiresApi 2com.github.droibit.flutter.plugins.customtabs.core  ResolveInfo 2com.github.droibit.flutter.plugins.customtabs.core  ResourceFactory 2com.github.droibit.flutter.plugins.customtabs.core  ResourcesCompat 2com.github.droibit.flutter.plugins.customtabs.core  Set 2com.github.droibit.flutter.plugins.customtabs.core  String 2com.github.droibit.flutter.plugins.customtabs.core  Suppress 2com.github.droibit.flutter.plugins.customtabs.core  SuppressLint 2com.github.droibit.flutter.plugins.customtabs.core  Uri 2com.github.droibit.flutter.plugins.customtabs.core  VisibleForTesting 2com.github.droibit.flutter.plugins.customtabs.core  apply 2com.github.droibit.flutter.plugins.customtabs.core  applyBrowserConfiguration 2com.github.droibit.flutter.plugins.customtabs.core  buildSet 2com.github.droibit.flutter.plugins.customtabs.core  bundleOf 2com.github.droibit.flutter.plugins.customtabs.core  extractPackageNames 2com.github.droibit.flutter.plugins.customtabs.core  fullIdentifierRegex 2com.github.droibit.flutter.plugins.customtabs.core  let 2com.github.droibit.flutter.plugins.customtabs.core  queryIntentActivities 2com.github.droibit.flutter.plugins.customtabs.core  setChromeCustomTabsPackage 2com.github.droibit.flutter.plugins.customtabs.core  setCustomTabsPackage 2com.github.droibit.flutter.plugins.customtabs.core  toBitmap 2com.github.droibit.flutter.plugins.customtabs.core  toRegex 2com.github.droibit.flutter.plugins.customtabs.core  Builder Ccom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntent  BrowserConfiguration Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  COLOR_SCHEME_DARK Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  COLOR_SCHEME_LIGHT Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  CustomTabsIntent Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  CustomTabsIntentOptions Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  
EXTRA_HEADERS Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  ID_NULL Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  ResourceFactory Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  apply Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  applyAnimations Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  applyBrowserConfiguration Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  applyCloseButton Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  applyColorSchemes Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  #applyPartialCustomTabsConfiguration Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  bundleOf Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  createIntent Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  createIntentOptions Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  let Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  	resources Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  setChromeCustomTabsPackage Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  setCustomTabsPackage Jcom.github.droibit.flutter.plugins.customtabs.core.CustomTabsIntentFactory  
EXTRA_HEADERS Jcom.github.droibit.flutter.plugins.customtabs.core.ExternalBrowserLauncher  Intent Jcom.github.droibit.flutter.plugins.customtabs.core.ExternalBrowserLauncher  bundleOf Jcom.github.droibit.flutter.plugins.customtabs.core.ExternalBrowserLauncher  createIntent Jcom.github.droibit.flutter.plugins.customtabs.core.ExternalBrowserLauncher  launch Jcom.github.droibit.flutter.plugins.customtabs.core.ExternalBrowserLauncher  let Jcom.github.droibit.flutter.plugins.customtabs.core.ExternalBrowserLauncher  Build Dcom.github.droibit.flutter.plugins.customtabs.core.NativeAppLauncher  Intent Dcom.github.droibit.flutter.plugins.customtabs.core.NativeAppLauncher  PackageManager Dcom.github.droibit.flutter.plugins.customtabs.core.NativeAppLauncher  Uri Dcom.github.droibit.flutter.plugins.customtabs.core.NativeAppLauncher  buildSet Dcom.github.droibit.flutter.plugins.customtabs.core.NativeAppLauncher  extractPackageNames Dcom.github.droibit.flutter.plugins.customtabs.core.NativeAppLauncher  launch Dcom.github.droibit.flutter.plugins.customtabs.core.NativeAppLauncher  launchNativeApi30 Dcom.github.droibit.flutter.plugins.customtabs.core.NativeAppLauncher  launchNativeBeforeApi30 Dcom.github.droibit.flutter.plugins.customtabs.core.NativeAppLauncher  queryIntentActivities Dcom.github.droibit.flutter.plugins.customtabs.core.NativeAppLauncher  Activity Lcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher  Boolean Lcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher  CustomTabsIntent Lcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher   EXTRA_INITIAL_ACTIVITY_HEIGHT_PX Lcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher  EXTRA_INITIAL_ACTIVITY_WIDTH_PX Lcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher   REQUEST_CODE_PARTIAL_CUSTOM_TABS Lcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher  Uri Lcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher  launch Lcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher   EXTRA_INITIAL_ACTIVITY_HEIGHT_PX Vcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher.Companion  EXTRA_INITIAL_ACTIVITY_WIDTH_PX Vcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher.Companion   REQUEST_CODE_PARTIAL_CUSTOM_TABS Vcom.github.droibit.flutter.plugins.customtabs.core.PartialCustomTabsLauncher.Companion  AnimRes Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  AnyRes Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  Bitmap Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  Build Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  Context Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  
ContextCompat Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  Double Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  DrawableCompat Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  Int Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  Px Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  ResourcesCompat Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  String Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  SuppressLint Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  convertToPx Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  fullIdentifierRegex Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  getAnimationIdentifier Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  	getBitmap Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  resolveIdentifier Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  toBitmap Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  toRegex Bcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory  Build Lcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory.Companion  
ContextCompat Lcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory.Companion  DrawableCompat Lcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory.Companion  ResourcesCompat Lcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory.Companion  fullIdentifierRegex Lcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory.Companion  toBitmap Lcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory.Companion  toRegex Lcom.github.droibit.flutter.plugins.customtabs.core.ResourceFactory.Companion  ActivityHeightResizeBehavior :com.github.droibit.flutter.plugins.customtabs.core.options  ActivitySideSheetDecorationType :com.github.droibit.flutter.plugins.customtabs.core.options  ActivitySideSheetPosition :com.github.droibit.flutter.plugins.customtabs.core.options  'ActivitySideSheetRoundedCornersPosition :com.github.droibit.flutter.plugins.customtabs.core.options  Any :com.github.droibit.flutter.plugins.customtabs.core.options  Boolean :com.github.droibit.flutter.plugins.customtabs.core.options  BrowserConfiguration :com.github.droibit.flutter.plugins.customtabs.core.options  Builder :com.github.droibit.flutter.plugins.customtabs.core.options  CloseButtonPosition :com.github.droibit.flutter.plugins.customtabs.core.options  Color :com.github.droibit.flutter.plugins.customtabs.core.options  ColorScheme :com.github.droibit.flutter.plugins.customtabs.core.options  Context :com.github.droibit.flutter.plugins.customtabs.core.options  CustomTabColorSchemeParams :com.github.droibit.flutter.plugins.customtabs.core.options  CustomTabsAnimations :com.github.droibit.flutter.plugins.customtabs.core.options  CustomTabsCloseButton :com.github.droibit.flutter.plugins.customtabs.core.options  CustomTabsColorSchemes :com.github.droibit.flutter.plugins.customtabs.core.options  CustomTabsIntentOptions :com.github.droibit.flutter.plugins.customtabs.core.options  CustomTabsPackageProvider :com.github.droibit.flutter.plugins.customtabs.core.options  CustomTabsSessionOptions :com.github.droibit.flutter.plugins.customtabs.core.options  	Dimension :com.github.droibit.flutter.plugins.customtabs.core.options  Double :com.github.droibit.flutter.plugins.customtabs.core.options  Int :com.github.droibit.flutter.plugins.customtabs.core.options  #KEY_ACTIVITY_HEIGHT_RESIZE_BEHAVIOR :com.github.droibit.flutter.plugins.customtabs.core.options  "KEY_ACTIVITY_SIDE_SHEET_BREAKPOINT :com.github.droibit.flutter.plugins.customtabs.core.options  'KEY_ACTIVITY_SIDE_SHEET_DECORATION_TYPE :com.github.droibit.flutter.plugins.customtabs.core.options  ,KEY_ACTIVITY_SIDE_SHEET_MAXIMIZATION_ENABLED :com.github.droibit.flutter.plugins.customtabs.core.options   KEY_ACTIVITY_SIDE_SHEET_POSITION :com.github.droibit.flutter.plugins.customtabs.core.options  0KEY_ACTIVITY_SIDE_SHEET_ROUNDED_CORNERS_POSITION :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_ANIMATIONS :com.github.droibit.flutter.plugins.customtabs.core.options  "KEY_BACKGROUND_INTERACTION_ENABLED :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_BOOKMARKS_BUTTON_ENABLED :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_BROWSER :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_CLOSE_BUTTON :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_COLOR_SCHEME :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_COLOR_SCHEMES :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_CORNER_RADIUS :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_DARK_PARAMS :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_DEFAULT_PARAMS :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_DOWNLOAD_BUTTON_ENABLED :com.github.droibit.flutter.plugins.customtabs.core.options  
KEY_END_ENTER :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_END_EXIT :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_FALLBACK_CUSTOM_TABS :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_HEADERS :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_ICON :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_INITIAL_HEIGHT :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_INITIAL_WIDTH :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_INSTANT_APPS_ENABLED :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_LIGHT_PARAMS :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_NAVIGATION_BAR_COLOR :com.github.droibit.flutter.plugins.customtabs.core.options   KEY_NAVIGATION_BAR_DIVIDER_COLOR :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_PARTIAL :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_POSITION :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_PREFERS_DEFAULT_BROWSER :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_PREFERS_EXTERNAL_BROWSER :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_SESSION_PACKAGE_NAME :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_SHARE_IDENTITY_ENABLED :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_SHARE_STATE :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_SHOW_TITLE :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_START_ENTER :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_START_EXIT :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_TOOLBAR_COLOR :com.github.droibit.flutter.plugins.customtabs.core.options  KEY_URL_BAR_HIDING_ENABLED :com.github.droibit.flutter.plugins.customtabs.core.options  List :com.github.droibit.flutter.plugins.customtabs.core.options  Long :com.github.droibit.flutter.plugins.customtabs.core.options  Map :com.github.droibit.flutter.plugins.customtabs.core.options  NonChromeCustomTabs :com.github.droibit.flutter.plugins.customtabs.core.options  PartialCustomTabsConfiguration :com.github.droibit.flutter.plugins.customtabs.core.options  Set :com.github.droibit.flutter.plugins.customtabs.core.options  
ShareState :com.github.droibit.flutter.plugins.customtabs.core.options  String :com.github.droibit.flutter.plugins.customtabs.core.options  Suppress :com.github.droibit.flutter.plugins.customtabs.core.options  let :com.github.droibit.flutter.plugins.customtabs.core.options  toSet :com.github.droibit.flutter.plugins.customtabs.core.options  Any Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  Boolean Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  BrowserConfiguration Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  Builder Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  Context Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  CustomTabsPackageProvider Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  KEY_FALLBACK_CUSTOM_TABS Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  KEY_HEADERS Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  KEY_PREFERS_DEFAULT_BROWSER Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  KEY_PREFERS_EXTERNAL_BROWSER Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  KEY_SESSION_PACKAGE_NAME Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  List Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  Map Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  NonChromeCustomTabs Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  Set Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  String Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  Suppress Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  fallbackCustomTabPackages Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  getAdditionalCustomTabs Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  headers Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  let Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  prefersDefaultBrowser Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  prefersExternalBrowser Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  sessionPackageName Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  toSet Ocom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration  Any Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  Boolean Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  BrowserConfiguration Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  Builder Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  KEY_FALLBACK_CUSTOM_TABS Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  KEY_HEADERS Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  KEY_PREFERS_DEFAULT_BROWSER Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  KEY_PREFERS_EXTERNAL_BROWSER Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  KEY_SESSION_PACKAGE_NAME Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  List Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  Map Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  Set Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  String Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  Suppress Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  build Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  fallbackCustomTabs Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  headers Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  prefersDefaultBrowser Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  prefersExternalBrowser Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  sessionPackageName Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  setFallbackCustomTabs Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  
setOptions Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  setPrefersDefaultBrowser Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  toSet Wcom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder  BrowserConfiguration acom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder.Companion  KEY_FALLBACK_CUSTOM_TABS acom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder.Companion  KEY_HEADERS acom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder.Companion  KEY_PREFERS_DEFAULT_BROWSER acom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder.Companion  KEY_PREFERS_EXTERNAL_BROWSER acom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder.Companion  KEY_SESSION_PACKAGE_NAME acom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder.Companion  toSet acom.github.droibit.flutter.plugins.customtabs.core.options.BrowserConfiguration.Builder.Companion  Any Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  Builder Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  CustomTabsAnimations Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  
KEY_END_ENTER Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  KEY_END_EXIT Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  KEY_START_ENTER Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  KEY_START_EXIT Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  Map Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  String Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  endEnter Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  endExit Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  let Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  
startEnter Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  	startExit Ocom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations  Any Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  Builder Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  CustomTabsAnimations Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  
KEY_END_ENTER Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  KEY_END_EXIT Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  KEY_START_ENTER Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  KEY_START_EXIT Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  Map Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  String Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  build Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  endEnter Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  endExit Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  
setOptions Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  
startEnter Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  	startExit Wcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder  CustomTabsAnimations acom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder.Companion  
KEY_END_ENTER acom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder.Companion  KEY_END_EXIT acom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder.Companion  KEY_START_ENTER acom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder.Companion  KEY_START_EXIT acom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsAnimations.Builder.Companion  Any Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  Builder Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  CloseButtonPosition Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  CustomTabsCloseButton Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  Int Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  KEY_ICON Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  KEY_POSITION Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  Long Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  Map Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  String Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  icon Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  let Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  position Pcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton  Any Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  Builder Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  CustomTabsCloseButton Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  Int Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  KEY_ICON Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  KEY_POSITION Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  Long Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  Map Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  String Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  build Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  icon Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  position Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  
setOptions Xcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder  CustomTabsCloseButton bcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder.Companion  KEY_ICON bcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder.Companion  KEY_POSITION bcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsCloseButton.Builder.Companion  Any Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  Builder Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  Color Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  ColorScheme Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  CustomTabColorSchemeParams Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  CustomTabsColorSchemes Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  Int Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  KEY_COLOR_SCHEME Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  KEY_DARK_PARAMS Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  KEY_DEFAULT_PARAMS Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  KEY_LIGHT_PARAMS Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  KEY_NAVIGATION_BAR_COLOR Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes   KEY_NAVIGATION_BAR_DIVIDER_COLOR Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  KEY_TOOLBAR_COLOR Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  Long Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  Map Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  String Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  Suppress Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  colorScheme Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  
darkParams Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  defaultPrams Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  let Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  lightParams Qcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes  Any Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  Builder Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  Color Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  ColorScheme Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  CustomTabColorSchemeParams Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  CustomTabsColorSchemes Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  Int Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  KEY_COLOR_SCHEME Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  KEY_DARK_PARAMS Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  KEY_DEFAULT_PARAMS Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  KEY_LIGHT_PARAMS Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  KEY_NAVIGATION_BAR_COLOR Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder   KEY_NAVIGATION_BAR_DIVIDER_COLOR Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  KEY_TOOLBAR_COLOR Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  Long Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  Map Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  String Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  Suppress Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  build Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  buildColorSchemeParams Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  colorScheme Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  
darkParams Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  
defaultParams Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  lightParams Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  
setOptions Ycom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder  Color ccom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder.Companion  CustomTabColorSchemeParams ccom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder.Companion  CustomTabsColorSchemes ccom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder.Companion  KEY_COLOR_SCHEME ccom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder.Companion  KEY_DARK_PARAMS ccom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder.Companion  KEY_DEFAULT_PARAMS ccom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder.Companion  KEY_LIGHT_PARAMS ccom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder.Companion  KEY_NAVIGATION_BAR_COLOR ccom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder.Companion   KEY_NAVIGATION_BAR_DIVIDER_COLOR ccom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder.Companion  KEY_TOOLBAR_COLOR ccom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsColorSchemes.Builder.Companion  Any Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  Boolean Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  BrowserConfiguration Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  Builder Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  CustomTabsAnimations Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  CustomTabsCloseButton Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  CustomTabsColorSchemes Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  CustomTabsIntentOptions Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  Int Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_ANIMATIONS Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_BOOKMARKS_BUTTON_ENABLED Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_BROWSER Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_CLOSE_BUTTON Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_COLOR_SCHEMES Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_DOWNLOAD_BUTTON_ENABLED Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_INSTANT_APPS_ENABLED Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_PARTIAL Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_SHARE_IDENTITY_ENABLED Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_SHARE_STATE Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_SHOW_TITLE Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  KEY_URL_BAR_HIDING_ENABLED Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  Long Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  Map Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  PartialCustomTabsConfiguration Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  
ShareState Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  String Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  Suppress Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  
animations Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  bookmarksButtonEnabled Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  browser Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  closeButton Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  colorSchemes Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  downloadButtonEnabled Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  instantAppsEnabled Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  partial Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  shareIdentityEnabled Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  
shareState Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  	showTitle Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  urlBarHidingEnabled Rcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions  Any Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  Boolean Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  BrowserConfiguration Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  Builder Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  CustomTabsAnimations Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  CustomTabsCloseButton Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  CustomTabsColorSchemes Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  CustomTabsIntentOptions Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  Int Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_ANIMATIONS Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_BOOKMARKS_BUTTON_ENABLED Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_BROWSER Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_CLOSE_BUTTON Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_COLOR_SCHEMES Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_DOWNLOAD_BUTTON_ENABLED Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_INSTANT_APPS_ENABLED Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_PARTIAL Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_SHARE_IDENTITY_ENABLED Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_SHARE_STATE Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_SHOW_TITLE Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  KEY_URL_BAR_HIDING_ENABLED Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  Long Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  Map Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  PartialCustomTabsConfiguration Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  
ShareState Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  String Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  Suppress Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  
animations Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  bookmarksButtonEnabled Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  browser Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  build Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  closeButton Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  colorSchemes Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  downloadButtonEnabled Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  instantAppsEnabled Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  partial Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  
setOptions Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  shareIdentityEnabled Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  
shareState Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  	showTitle Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  urlBarHidingEnabled Zcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder  BrowserConfiguration dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  CustomTabsAnimations dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  CustomTabsCloseButton dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  CustomTabsColorSchemes dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  CustomTabsIntentOptions dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_ANIMATIONS dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_BOOKMARKS_BUTTON_ENABLED dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_BROWSER dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_CLOSE_BUTTON dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_COLOR_SCHEMES dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_DOWNLOAD_BUTTON_ENABLED dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_INSTANT_APPS_ENABLED dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_PARTIAL dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_SHARE_IDENTITY_ENABLED dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_SHARE_STATE dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_SHOW_TITLE dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  KEY_URL_BAR_HIDING_ENABLED dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  PartialCustomTabsConfiguration dcom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsIntentOptions.Builder.Companion  Any Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  Boolean Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  BrowserConfiguration Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  Builder Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  Context Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  CustomTabsPackageProvider Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  CustomTabsSessionOptions Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  KEY_FALLBACK_CUSTOM_TABS Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  KEY_PREFERS_DEFAULT_BROWSER Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  List Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  Map Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  Set Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  String Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  Suppress Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  browser Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  getAdditionalCustomTabs Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  prefersDefaultBrowser Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  toSet Scom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions  Any [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  Boolean [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  Builder [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  CustomTabsSessionOptions [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  KEY_FALLBACK_CUSTOM_TABS [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  KEY_PREFERS_DEFAULT_BROWSER [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  List [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  Map [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  Set [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  String [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  Suppress [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  build [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  fallbackCustomTabs [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  prefersDefaultBrowser [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  
setOptions [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  toSet [com.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder  CustomTabsSessionOptions ecom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder.Companion  KEY_FALLBACK_CUSTOM_TABS ecom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder.Companion  KEY_PREFERS_DEFAULT_BROWSER ecom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder.Companion  toSet ecom.github.droibit.flutter.plugins.customtabs.core.options.CustomTabsSessionOptions.Builder.Companion  ActivityHeightResizeBehavior Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  ActivitySideSheetDecorationType Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  ActivitySideSheetPosition Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  'ActivitySideSheetRoundedCornersPosition Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  Any Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  Boolean Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  Builder Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  	Dimension Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  Double Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  Int Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  #KEY_ACTIVITY_HEIGHT_RESIZE_BEHAVIOR Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  "KEY_ACTIVITY_SIDE_SHEET_BREAKPOINT Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  'KEY_ACTIVITY_SIDE_SHEET_DECORATION_TYPE Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  ,KEY_ACTIVITY_SIDE_SHEET_MAXIMIZATION_ENABLED Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration   KEY_ACTIVITY_SIDE_SHEET_POSITION Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  0KEY_ACTIVITY_SIDE_SHEET_ROUNDED_CORNERS_POSITION Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  "KEY_BACKGROUND_INTERACTION_ENABLED Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  KEY_CORNER_RADIUS Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  KEY_INITIAL_HEIGHT Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  KEY_INITIAL_WIDTH Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  Long Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  Map Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  PartialCustomTabsConfiguration Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  String Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  activityHeightResizeBehavior Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  activitySideSheetBreakpoint Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  activitySideSheetDecorationType Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  $activitySideSheetMaximizationEnabled Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  activitySideSheetPosition Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  'activitySideSheetRoundedCornersPosition Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  backgroundInteractionEnabled Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  cornerRadius Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  
initialHeight Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  initialWidth Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  let Ycom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration  ActivityHeightResizeBehavior acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  ActivitySideSheetDecorationType acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  ActivitySideSheetPosition acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  'ActivitySideSheetRoundedCornersPosition acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  Any acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  Boolean acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  Builder acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  	Dimension acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  Double acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  Int acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  #KEY_ACTIVITY_HEIGHT_RESIZE_BEHAVIOR acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  "KEY_ACTIVITY_SIDE_SHEET_BREAKPOINT acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  'KEY_ACTIVITY_SIDE_SHEET_DECORATION_TYPE acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  ,KEY_ACTIVITY_SIDE_SHEET_MAXIMIZATION_ENABLED acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder   KEY_ACTIVITY_SIDE_SHEET_POSITION acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  0KEY_ACTIVITY_SIDE_SHEET_ROUNDED_CORNERS_POSITION acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  "KEY_BACKGROUND_INTERACTION_ENABLED acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  KEY_CORNER_RADIUS acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  KEY_INITIAL_HEIGHT acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  KEY_INITIAL_WIDTH acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  Long acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  Map acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  PartialCustomTabsConfiguration acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  String acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  activityHeightResizeBehavior acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  activitySideSheetBreakpoint acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  activitySideSheetDecorationType acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  $activitySideSheetMaximizationEnabled acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  activitySideSheetPosition acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  'activitySideSheetRoundedCornersPosition acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  backgroundInteractionEnabled acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  build acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  cornerRadius acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  
initialHeight acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  initialWidth acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  
setOptions acom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder  	Dimension kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  #KEY_ACTIVITY_HEIGHT_RESIZE_BEHAVIOR kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  "KEY_ACTIVITY_SIDE_SHEET_BREAKPOINT kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  'KEY_ACTIVITY_SIDE_SHEET_DECORATION_TYPE kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  ,KEY_ACTIVITY_SIDE_SHEET_MAXIMIZATION_ENABLED kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion   KEY_ACTIVITY_SIDE_SHEET_POSITION kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  0KEY_ACTIVITY_SIDE_SHEET_ROUNDED_CORNERS_POSITION kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  "KEY_BACKGROUND_INTERACTION_ENABLED kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  KEY_CORNER_RADIUS kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  KEY_INITIAL_HEIGHT kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  KEY_INITIAL_WIDTH kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  PartialCustomTabsConfiguration kcom.github.droibit.flutter.plugins.customtabs.core.options.PartialCustomTabsConfiguration.Builder.Companion  Any :com.github.droibit.flutter.plugins.customtabs.core.session  Boolean :com.github.droibit.flutter.plugins.customtabs.core.session  
ComponentName :com.github.droibit.flutter.plugins.customtabs.core.session  Context :com.github.droibit.flutter.plugins.customtabs.core.session  CustomTabsClient :com.github.droibit.flutter.plugins.customtabs.core.session  CustomTabsServiceConnection :com.github.droibit.flutter.plugins.customtabs.core.session  CustomTabsSession :com.github.droibit.flutter.plugins.customtabs.core.session  CustomTabsSessionController :com.github.droibit.flutter.plugins.customtabs.core.session  CustomTabsSessionManager :com.github.droibit.flutter.plugins.customtabs.core.session  CustomTabsSessionOptions :com.github.droibit.flutter.plugins.customtabs.core.session  CustomTabsSessionProvider :com.github.droibit.flutter.plugins.customtabs.core.session  KEY_URL :com.github.droibit.flutter.plugins.customtabs.core.session  List :com.github.droibit.flutter.plugins.customtabs.core.session  Log :com.github.droibit.flutter.plugins.customtabs.core.session  Map :com.github.droibit.flutter.plugins.customtabs.core.session  
MutableMap :com.github.droibit.flutter.plugins.customtabs.core.session  SecurityException :com.github.droibit.flutter.plugins.customtabs.core.session  String :com.github.droibit.flutter.plugins.customtabs.core.session  TAG :com.github.droibit.flutter.plugins.customtabs.core.session  VisibleForTesting :com.github.droibit.flutter.plugins.customtabs.core.session  also :com.github.droibit.flutter.plugins.customtabs.core.session  bundleOf :com.github.droibit.flutter.plugins.customtabs.core.session  getCustomTabsPackage :com.github.droibit.flutter.plugins.customtabs.core.session  let :com.github.droibit.flutter.plugins.customtabs.core.session  map :com.github.droibit.flutter.plugins.customtabs.core.session  mutableMapOf :com.github.droibit.flutter.plugins.customtabs.core.session  set :com.github.droibit.flutter.plugins.customtabs.core.session  to :com.github.droibit.flutter.plugins.customtabs.core.session  toUri :com.github.droibit.flutter.plugins.customtabs.core.session  Boolean Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  
ComponentName Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  Context Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  CustomTabsClient Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  CustomTabsSession Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  KEY_URL Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  List Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  Log Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  SecurityException Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  String Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  TAG Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  VisibleForTesting Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  also Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  bindCustomTabsService Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  bundleOf Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  context Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  isCustomTabsServiceBound Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  map Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  
mayLaunchUrls Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  packageName Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  session Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  to Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  toUri Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  tryBindCustomTabsService Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  unbindCustomTabsService Vcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController  CustomTabsClient `com.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController.Companion  KEY_URL `com.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController.Companion  Log `com.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController.Companion  TAG `com.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController.Companion  bundleOf `com.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController.Companion  map `com.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController.Companion  to `com.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController.Companion  toUri `com.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionController.Companion  CustomTabsSessionController Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  CustomTabsSessionOptions Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  also Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  cachedSessions Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  createSessionController Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  createSessionOptions Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  getCustomTabsPackage Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  getSessionController Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  handleActivityChange Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  invalidateSession Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  let Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  mutableMapOf Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  set Scom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionManager  
getSession Tcom.github.droibit.flutter.plugins.customtabs.core.session.CustomTabsSessionProvider  Bundle 8com.github.droibit.flutter.plugins.customtabs.core.utils  Map 8com.github.droibit.flutter.plugins.customtabs.core.utils  String 8com.github.droibit.flutter.plugins.customtabs.core.utils  apply 8com.github.droibit.flutter.plugins.customtabs.core.utils  bundleOf 8com.github.droibit.flutter.plugins.customtabs.core.utils  
component1 8com.github.droibit.flutter.plugins.customtabs.core.utils  
component2 8com.github.droibit.flutter.plugins.customtabs.core.utils  iterator 8com.github.droibit.flutter.plugins.customtabs.core.utils  Log 
io.flutter  d io.flutter.Log  w io.flutter.Log  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BasicMessageChannel io.flutter.plugin.common  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  MessageCodec io.flutter.plugin.common  StandardMessageCodec io.flutter.plugin.common  StandardMethodCodec io.flutter.plugin.common  MessageHandler ,io.flutter.plugin.common.BasicMessageChannel  Reply ,io.flutter.plugin.common.BasicMessageChannel  setMessageHandler ,io.flutter.plugin.common.BasicMessageChannel  <SAM-CONSTRUCTOR> ;io.flutter.plugin.common.BasicMessageChannel.MessageHandler  reply 2io.flutter.plugin.common.BasicMessageChannel.Reply  readValueOfType -io.flutter.plugin.common.StandardMessageCodec  
writeValue -io.flutter.plugin.common.StandardMessageCodec  ByteArrayOutputStream java.io  Class 	java.lang  SecurityException 	java.lang  
simpleName java.lang.Class  
ByteBuffer java.nio  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Lazy kotlin  Nothing kotlin  Pair kotlin  Result kotlin  Suppress kotlin  	Throwable kotlin  also kotlin  apply kotlin  getValue kotlin  lazy kotlin  let kotlin  map kotlin  requireNotNull kotlin  run kotlin  to kotlin  toString 
kotlin.Any  let kotlin.Boolean  let 
kotlin.Double  plus 
kotlin.Double  times 
kotlin.Double  toInt 
kotlin.Double  	compareTo 
kotlin.Int  let 
kotlin.Int  or 
kotlin.Int  toLong 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  toInt kotlin.Long  
isNotEmpty 
kotlin.String  let 
kotlin.String  plus 
kotlin.String  to 
kotlin.String  toRegex 
kotlin.String  toUri 
kotlin.String  cause kotlin.Throwable  	javaClass kotlin.Throwable  message kotlin.Throwable  toString kotlin.Throwable  ByteIterator kotlin.collections  CharIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  buildSet kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  getValue kotlin.collections  
isNotEmpty kotlin.collections  iterator kotlin.collections  listOf kotlin.collections  map kotlin.collections  mutableMapOf kotlin.collections  set kotlin.collections  toSet kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  get kotlin.collections.List  isEmpty kotlin.collections.List  iterator kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  toSet kotlin.collections.List  Entry kotlin.collections.Map  get kotlin.collections.Map  iterator kotlin.collections.Map  let kotlin.collections.Map  size kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  iterator $kotlin.collections.MutableCollection  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  iterator kotlin.collections.MutableList  MutableEntry kotlin.collections.MutableMap  get kotlin.collections.MutableMap  remove kotlin.collections.MutableMap  set kotlin.collections.MutableMap  values kotlin.collections.MutableMap  add kotlin.collections.MutableSet  addAll kotlin.collections.MutableSet  extractPackageNames kotlin.collections.MutableSet  queryIntentActivities kotlin.collections.MutableSet  	removeAll kotlin.collections.MutableSet  isEmpty kotlin.collections.Set  let kotlin.collections.Set  iterator 	kotlin.io  JvmOverloads 
kotlin.jvm  	javaClass 
kotlin.jvm  KClass kotlin.reflect  
KProperty1 kotlin.reflect  Sequence kotlin.sequences  iterator kotlin.sequences  map kotlin.sequences  toSet kotlin.sequences  Regex kotlin.text  
isNotEmpty kotlin.text  iterator kotlin.text  map kotlin.text  set kotlin.text  toRegex kotlin.text  toSet kotlin.text  containsMatchIn kotlin.text.Regex                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;

void main() {
  group('Link Verification Tests', () {
    test('SEE website should be accessible', () async {
      try {
        final response = await http.head(Uri.parse('https://see.gov.np'));
        expect(response.statusCode, lessThan(400));
      } catch (e) {
        // Network issues are acceptable in tests
        print('SEE link test skipped due to network: $e');
      }
    });

    test('NEB website should be accessible', () async {
      try {
        final response = await http.head(Uri.parse('https://neb.gov.np'));
        expect(response.statusCode, lessThan(400));
      } catch (e) {
        print('NEB link test skipped due to network: $e');
      }
    });

    test('TU website should be accessible', () async {
      try {
        final response = await http.head(Uri.parse('https://tribhuvan-university.edu.np'));
        expect(response.statusCode, lessThan(400));
      } catch (e) {
        print('TU link test skipped due to network: $e');
      }
    });

    test('CTEVT website should be accessible', () async {
      try {
        final response = await http.head(Uri.parse('https://ctevt.org.np'));
        expect(response.statusCode, lessThan(400));
      } catch (e) {
        print('CTEVT link test skipped due to network: $e');
      }
    });

    test('KU website should be accessible', () async {
      try {
        final response = await http.head(Uri.parse('https://ku.edu.np'));
        expect(response.statusCode, lessThan(400));
      } catch (e) {
        print('KU link test skipped due to network: $e');
      }
    });

    test('PU website should be accessible', () async {
      try {
        final response = await http.head(Uri.parse('https://purbuniv.edu.np'));
        expect(response.statusCode, lessThan(400));
      } catch (e) {
        print('PU link test skipped due to network: $e');
      }
    });

    test('DV Lottery website should be accessible', () async {
      try {
        final response = await http.head(Uri.parse('https://dvprogram.state.gov'));
        expect(response.statusCode, lessThan(400));
      } catch (e) {
        print('DV Lottery link test skipped due to network: $e');
      }
    });

    test('IPO Result CDSC website should be accessible', () async {
      try {
        final response = await http.head(Uri.parse('https://iporesult.cdsc.com.np'));
        expect(response.statusCode, lessThan(400));
      } catch (e) {
        print('IPO CDSC link test skipped due to network: $e');
      }
    });

    test('Meroshare website should be accessible', () async {
      try {
        final response = await http.head(Uri.parse('https://meroshare.cdsc.com.np'));
        expect(response.statusCode, lessThan(400));
      } catch (e) {
        print('Meroshare link test skipped due to network: $e');
      }
    });

    test('Books website should be accessible', () async {
      try {
        final response = await http.head(Uri.parse('https://moecdc.gov.np/en/text-books'));
        expect(response.statusCode, lessThan(400));
      } catch (e) {
        print('Books link test skipped due to network: $e');
      }
    });
  });
}

<component name="libraryTable">
  <library name="Dart SDK">
    <CLASSES>
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/async" />
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/collection" />
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/convert" />
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/core" />
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/developer" />
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/html" />
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/io" />
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/isolate" />
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/math" />
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/mirrors" />
      <root url="file:///Users/<USER>/Downloads/flutter/bin/cache/dart-sdk/lib/typed_data" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>